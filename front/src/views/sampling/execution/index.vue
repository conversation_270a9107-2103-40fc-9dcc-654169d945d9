<template>
  <div class="app-container">
    <!-- Tab切换 -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="execution-tabs">
      <el-tab-pane label="我的执行任务" name="my-tasks"></el-tab-pane>
      <el-tab-pane label="所有执行任务" name="all-tasks" v-if="isAdmin"></el-tab-pane>
    </el-tabs>

    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch">
      <el-form-item label="任务名称" prop="taskName">
        <el-input
          v-model="queryParams.taskName"
          placeholder="请输入任务名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择任务状态" clearable style="width: 200px">
          <el-option
            v-for="dict in dict.type.sampling_task_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="executionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="分组编号" align="center" prop="groupCode" width="150" :show-overflow-tooltip="true" />
      <el-table-column label="任务名称" align="center" prop="taskName" :show-overflow-tooltip="true" />
      <el-table-column label="项目名称" align="center" prop="projectName" :show-overflow-tooltip="true" />
      <el-table-column label="报价单编号" align="center" prop="projectCode" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="客户名称" align="center" prop="customerName" :show-overflow-tooltip="true" />
      <el-table-column label="合同信息" align="center" width="180">
        <template #default="scope">
          <div class="contract-info-table">
            <div v-if="scope.row.contractName" class="contract-name">
              <el-icon><Document /></el-icon>
              {{ scope.row.contractName }}
            </div>
            <div v-if="scope.row.contractNumber" class="contract-number">
              编号: {{ scope.row.contractNumber }}
            </div>
            <div v-if="!scope.row.contractName && !scope.row.contractNumber" class="no-contract">
              <el-text type="info" size="small">暂无合同</el-text>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="方案详情" align="center" width="150">
        <template #default="scope">
          <div class="scheme-details-table">
            <div v-if="scope.row.schemeDetails">
              <div class="detail-row">
                <el-tag type="info" size="small">频次: {{ scope.row.schemeDetails.frequency || 0 }}次</el-tag>
              </div>
              <div class="detail-row">
                <el-tag type="success" size="small">样品: {{ scope.row.schemeDetails.sampleCount || 0 }}个</el-tag>
              </div>
            </div>
            <div v-else class="no-scheme">
              <el-text type="info" size="small">暂无方案</el-text>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="分组信息" align="center" width="200">
        <template #default="scope">
          <div class="group-info">
            <p><strong>周期:</strong> {{ scope.row.cycleNumber }} ({{ scope.row.cycleType }})</p>
            <p><strong>类别:</strong> {{ scope.row.detectionCategory }}</p>
            <p><strong>点位:</strong> {{ scope.row.pointName }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="执行人" align="center" prop="assignedUserNames" width="150">
        <template #default="scope">
          <el-tag v-for="name in scope.row.assignedUserNames" :key="name" size="small" style="margin: 2px;">
            {{ name }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="任务状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="dict.type.sampling_task_status" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="计划开始日期" align="center" prop="plannedStartDate" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.plannedStartDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="计划结束日期" align="center" prop="plannedEndDate" width="120">
        <template #default="scope">
          <span>{{ parseTime(scope.row.plannedEndDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="View"
            @click="handleDetail(scope.row)"
          >详情</el-button>
          <el-button
            type="text"
            icon="VideoPlay"
            @click="handleExecute(scope.row)"
            v-if="scope.row.status === 0"
          >开始执行</el-button>
          <el-button
            type="text"
            icon="Check"
            @click="handleComplete(scope.row)"
            v-if="scope.row.status === 1"
          >完成任务</el-button>
          <el-button
            type="text"
            icon="Document"
            @click="handleSamplingManagement(scope.row)"
            v-if="scope.row.status === 1"
          >采样管理</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 任务详情对话框 -->
    <el-dialog title="任务详情" v-model="detailOpen" width="1200px" append-to-body class="detail-dialog">
      <div class="detail-content">
        <el-descriptions class="detail-descriptions" :column="3" border>
          <el-descriptions-item label="任务编号">{{ taskDetail.taskCode }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ taskDetail.taskName }}</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <dict-tag :options="dict.type.sampling_task_status" :value="taskDetail.status"/>
          </el-descriptions-item>
          <el-descriptions-item label="项目名称">{{ taskDetail.projectName }}</el-descriptions-item>
          <el-descriptions-item label="报价单编号">{{ taskDetail.projectCode || '暂无编号' }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ taskDetail.customerName }}</el-descriptions-item>
          <el-descriptions-item label="执行人员">{{ taskDetail.assignedUserNames ? taskDetail.assignedUserNames.join(', ') : '未分配' }}</el-descriptions-item>
          <el-descriptions-item label="周期信息">第{{ taskDetail.cycleNumber }}周期 ({{ taskDetail.cycleType }})</el-descriptions-item>
          <el-descriptions-item label="检测类别">{{ taskDetail.detectionCategory }}</el-descriptions-item>
          <el-descriptions-item label="点位名称">{{ taskDetail.pointName }}</el-descriptions-item>
          <el-descriptions-item label="总样品数量">{{ getTotalSampleCount(taskDetail.cycleItems) }}个</el-descriptions-item>
          <el-descriptions-item label="检测参数数量">{{ taskDetail.cycleItems ? taskDetail.cycleItems.length : 0 }}项</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ parseTime(taskDetail.createTime, '{y}-{m}-{d} {h}:{i}') }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 分组周期条目 -->
        <div v-if="taskDetail.cycleItems && taskDetail.cycleItems.length > 0" style="margin-top: 20px;">
          <h3 style="margin-bottom: 16px; color: var(--el-text-color-primary);">分组周期条目详情</h3>
          <el-table :data="taskDetail.cycleItems" border style="width: 100%">
            <el-table-column prop="cycleNumber" label="周期序号" width="100" align="center" />
            <el-table-column prop="category" label="检测类别" width="120" align="center" />
            <el-table-column prop="parameter" label="检测参数" min-width="150" show-overflow-tooltip />
            <el-table-column prop="pointName" label="点位名称" width="120" align="center" />
            <el-table-column prop="sampleCount" label="样品数量" width="100" align="center" />
            <el-table-column prop="frequency" label="采样频率" width="100" align="center">
              <template #default="scope">
                {{ scope.row.frequency || 1 }}次
              </template>
            </el-table-column>
            <el-table-column prop="cycleType" label="周期类型" width="100" align="center" />
            <el-table-column prop="method" label="检测方法" min-width="200" show-overflow-tooltip />
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="scope">
                <el-tag :type="getCycleItemStatusType(scope.row.status)" size="small">
                  {{ getCycleItemStatusLabel(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>

    <!-- 采样管理对话框 -->
    <el-dialog title="采样管理" v-model="sampleManagementOpen" width="1400px" append-to-body class="sample-management-dialog" @close="handleSampleManagementClose">
      <div class="sample-management-content">
        <!-- 任务信息 -->
        <el-descriptions class="task-info" :column="4" border>
          <el-descriptions-item label="分组编号">{{ currentAssignment.groupCode }}</el-descriptions-item>
          <el-descriptions-item label="任务名称">{{ currentAssignment.taskName }}</el-descriptions-item>
          <el-descriptions-item label="项目名称">{{ currentAssignment.projectName }}</el-descriptions-item>
          <el-descriptions-item label="报价单编号">{{ currentAssignment.projectCode }}</el-descriptions-item>
          <el-descriptions-item label="客户名称">{{ currentAssignment.customerName }}</el-descriptions-item>
          <el-descriptions-item label="合同名称">{{ currentAssignment.contractName || '暂无合同' }}</el-descriptions-item>
          <el-descriptions-item label="合同编号">{{ currentAssignment.contractNumber || '暂无编号' }}</el-descriptions-item>
          <el-descriptions-item label="周期序号">{{ currentAssignment.cycleNumber }}</el-descriptions-item>
          <el-descriptions-item label="周期类型">{{ currentAssignment.cycleType }}</el-descriptions-item>
          <el-descriptions-item label="检测类别">{{ currentAssignment.detectionCategory }}</el-descriptions-item>
          <el-descriptions-item label="点位名称">{{ currentAssignment.pointName }}</el-descriptions-item>
          <el-descriptions-item label="方案详情" :span="4">
            <div v-if="currentAssignment.schemeDetails" class="scheme-details-inline">
              <el-tag type="info" size="small" style="margin-right: 8px;">
                频次: {{ currentAssignment.schemeDetails.frequency || 0 }}次
              </el-tag>
              <el-tag type="success" size="small">
                样品: {{ currentAssignment.schemeDetails.sampleCount || 0 }}个
              </el-tag>
            </div>
            <el-text v-else type="info" size="small">暂无方案详情</el-text>
          </el-descriptions-item>
        </el-descriptions>

        <!-- Tab页切换 -->
        <el-tabs v-model="activeManagementTab" class="management-tabs" style="margin-top: 20px;">
          <el-tab-pane label="样品管理" name="samples">
            <!-- 样品记录管理 -->
            <div class="sample-management-section">

            <!-- 统计信息 -->
            <div class="sample-statistics" style="margin: 16px 0;">
              <el-row :gutter="12">
                <el-col :span="4">
                  <div class="statistic-card total">
                    <el-statistic title="总数量" :value="sampleStatistics.totalCount">
                      <template #prefix>
                        <el-icon style="color: #409eff;"><Document /></el-icon>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
                <el-col :span="3">
                  <div class="statistic-card pending">
                    <el-statistic title="待采集" :value="sampleStatistics.pendingCount">
                      <template #prefix>
                        <el-icon style="color: #e6a23c;"><Clock /></el-icon>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
                <el-col :span="3">
                  <div class="statistic-card collecting">
                    <el-statistic title="采集中" :value="sampleStatistics.collectingCount">
                      <template #prefix>
                        <el-icon style="color: #fa8c16;"><Loading /></el-icon>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
                <el-col :span="3">
                  <div class="statistic-card collected">
                    <el-statistic title="已采集" :value="sampleStatistics.collectedCount">
                      <template #prefix>
                        <el-icon style="color: #67c23a;"><Check /></el-icon>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
                <el-col :span="3">
                  <div class="statistic-card submitted">
                    <el-statistic title="已提交" :value="sampleStatistics.submittedCount">
                      <template #prefix>
                        <el-icon style="color: #409eff;"><Upload /></el-icon>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
                <el-col :span="4">
                  <div class="statistic-card testing">
                    <el-statistic title="检测中" :value="sampleStatistics.testingCount">
                      <template #prefix>
                        <el-icon style="color: #f56c6c;"><Loading /></el-icon>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
                <el-col :span="4">
                  <div class="statistic-card completed">
                    <el-statistic title="已完成" :value="sampleStatistics.completedCount">
                      <template #prefix>
                        <el-icon style="color: #67c23a;"><CircleCheck /></el-icon>
                      </template>
                    </el-statistic>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- 操作按钮 -->
            <div class="sample-operations" style="margin: 12px 0;">
              <!-- 移除样品批量操作按钮，样品状态由瓶组状态自动计算 -->
              <el-button icon="Refresh" @click="refreshSampleList">刷新</el-button>
            </div>

            <!-- 样品记录列表 -->
            <el-table
              ref="sampleTableRef"
              v-loading="sampleLoading"
              :data="sampleRecords"
              :expand-row-keys="expandedSampleRows"
              row-key="id"
              @expand-change="handleSampleExpand"
              border
              size="small"
              :row-style="{ height: '40px' }"
              :cell-style="{ padding: '8px 12px' }"
              style="width: 100%"
            >
              <el-table-column type="expand" width="100" align="center" label="瓶组">
                <template #header>
                  <span style="font-size: 11px; color: var(--el-text-color-regular);">展开瓶组</span>
                </template>
                <template #default="props">
                  <div class="sample-bottle-groups" style="padding: 12px 16px; background-color: var(--el-bg-color-page);">
                    <h4 style="margin: 0 0 8px 0; color: var(--el-color-primary); font-size: 14px;">
                      <el-icon style="margin-right: 4px;"><Box /></el-icon>
                      瓶组信息
                    </h4>

                    <!-- 加载状态 -->
                    <div v-if="props.row.bottleGroupsLoading" class="bottle-groups-loading">
                      <el-skeleton :rows="2" animated>
                        <template #template>
                          <el-skeleton-item variant="rect" style="width: 100%; height: 120px; margin-bottom: 16px;" />
                          <el-skeleton-item variant="rect" style="width: 100%; height: 120px;" />
                        </template>
                      </el-skeleton>
                    </div>

                    <!-- 瓶组信息卡片式布局 -->
                    <div v-else-if="Array.isArray(props.row.bottleGroups) && props.row.bottleGroups.length > 0" class="bottle-groups-container">
                      <el-row :gutter="12">
                        <el-col :span="24" v-for="(bottle, index) in props.row.bottleGroups" :key="bottle.id" style="margin-bottom: 12px;">
                          <el-card shadow="hover" :body-style="{ padding: '12px' }" style="border-radius: 6px;">
                            <template #header>
                              <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px;">
                                <span style="font-weight: 600; color: var(--el-color-primary); font-size: 13px;">
                                  <el-icon style="margin-right: 4px; font-size: 12px;"><Box /></el-icon>
                                  {{ bottle.bottleGroupCode }}
                                </span>
                                <el-tag :type="getBottleStatusType(bottle.status)" size="small">
                                  {{ getBottleStatusLabel(bottle.status) }}
                                </el-tag>
                              </div>
                            </template>

                            <el-row :gutter="12">
                              <el-col :span="8">
                                <div class="bottle-info-item">
                                  <span class="label">类型：</span>
                                  <span class="value">{{ bottle.bottleType || '默认瓶组' }}</span>
                                </div>
                                <div class="bottle-info-item">
                                  <span class="label">容量：</span>
                                  <span class="value">{{ bottle.bottleVolume || '-' }}</span>
                                </div>
                              </el-col>
                              <el-col :span="8">
                                <div class="bottle-info-item">
                                  <span class="label">存储：</span>
                                  <span class="value">
                                    {{ bottle.storageStyles && bottle.storageStyles.length > 0 ? bottle.storageStyles.join(', ') : '-' }}
                                  </span>
                                </div>
                                <div class="bottle-info-item">
                                  <span class="label">时效：</span>
                                  <span class="value">
                                    {{ bottle.sampleAge ? `${bottle.sampleAge}${bottle.sampleAgeUnit}` : '-' }}
                                  </span>
                                </div>
                              </el-col>
                              <el-col :span="8">
                                <div class="bottle-info-item">
                                  <span class="label">操作：</span>
                                  <div class="value">
                                    <!-- 状态流转按钮 -->
                                    <el-button
                                      v-if="bottle.status < 3"
                                      type="primary"
                                      size="small"
                                      @click="handleBottleStatusUpdate(bottle, bottle.status + 1)"
                                      style="padding: 4px 8px; font-size: 12px; margin-right: 4px;"
                                    >
                                      {{ getNextStatusLabel(bottle.status) }}
                                    </el-button>

                                    <!-- 回退按钮 -->
                                    <el-button
                                      v-if="bottle.status > 0"
                                      type="warning"
                                      size="small"
                                      @click="handleBottleStatusUpdate(bottle, bottle.status - 1)"
                                      style="padding: 4px 8px; font-size: 12px; margin-right: 4px;"
                                    >
                                      回退
                                    </el-button>

                                    <!-- 打印按钮 -->
                                    <el-button
                                      type="info"
                                      icon="Printer"
                                      @click="handleBottlePrint(bottle)"
                                      size="small"
                                      style="padding: 4px 8px; font-size: 12px; margin-right: 4px;"
                                    >打印</el-button>
                                  </div>
                                </div>
                              </el-col>
                            </el-row>

                            <div class="bottle-info-item" style="margin-top: 8px;">
                              <span class="label">检测方法：</span>
                              <span class="value" style="word-break: break-all; font-size: 12px;">{{ bottle.detectionMethod || '-' }}</span>
                            </div>
                          </el-card>
                        </el-col>
                      </el-row>
                    </div>

                    <!-- 无瓶组信息 -->
                    <div v-else-if="Array.isArray(props.row.bottleGroups) && props.row.bottleGroups.length === 0" class="no-bottle-groups">
                      <el-empty description="暂无关联的瓶组信息" :image-size="80">
                        <template #image>
                          <el-icon size="80" color="var(--el-text-color-placeholder)"><Box /></el-icon>
                        </template>
                      </el-empty>
                    </div>

                    <!-- 未加载瓶组信息 -->
                    <div v-else class="no-bottle-groups">
                      <el-empty description="点击展开查看瓶组信息" :image-size="80">
                        <template #image>
                          <el-icon size="80" color="var(--el-text-color-placeholder)"><Box /></el-icon>
                        </template>
                      </el-empty>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="样品编号" align="center" prop="sampleNumber" width="150" show-overflow-tooltip>
                <template #default="scope">
                  <span :style="{ color: scope.row.isQualityControl ? '#e6a23c' : '' }">
                    {{ scope.row.sampleNumber }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="类型" align="center" prop="sampleType" width="80" />
              <el-table-column label="来源" align="center" prop="sampleSource" width="80" />
              <el-table-column label="点位名称" align="center" prop="pointName" width="120" />
              <el-table-column label="采样周期" align="center" width="100">
                <template #default="scope">
                  {{ scope.row.cycleNumber }}({{ scope.row.cycleType }})
                </template>
              </el-table-column>
              <el-table-column label="检测类别" align="center" prop="detectionCategory" width="100" />
              <el-table-column label="检测参数" align="center" prop="detectionParameter" min-width="120" show-overflow-tooltip />
              <el-table-column label="检测方法" align="center" prop="detectionMethod" min-width="120" show-overflow-tooltip />
              <el-table-column label="状态" align="center" prop="status" width="80">
                <template #default="scope">
                  <el-tag :type="getSampleStatusType(scope.row.status)" size="small">
                    {{ getSampleStatusLabel(scope.row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="采集时间" align="center" prop="collectionTime" width="150">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.collectionTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                </template>
              </el-table-column>
              <el-table-column label="质控样" align="center" width="100">
                <template #default="scope">
                  <el-tag v-if="scope.row.isQualityControl" type="warning" size="small">
                    {{ getQualityControlTypeLabel(scope.row.qualityControlType) }}
                  </el-tag>
                  <span v-else-if="scope.row.qualityControlCount > 0" style="color: var(--el-color-primary); font-size: 12px;">
                    关联{{ scope.row.qualityControlCount }}个
                  </span>
                  <span v-else>-</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="120">
                <template #default="scope">
                  <el-button
                    v-if="!scope.row.isQualityControl"
                    type="primary"
                    size="small"
                    @click="showAddQualityControlForSample(scope.row)"
                    style="padding: 4px 8px; font-size: 12px;"
                  >
                    添加质控样
                  </el-button>
                  <el-tag v-else type="info" size="small">
                    关联样品{{ scope.row.relatedSampleNumber }}
                  </el-tag>
                </template>
              </el-table-column>

            </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="点位信息" name="pointInfo">
            <!-- 点位信息内容 -->
            <div class="point-info-content">
              <PointInfoDialog
                :group-id="currentGroupId"
                :inline="true"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-dialog>

    <!-- 样品标签打印弹窗 -->
    <SampleLabelPrint
      v-model="printDialogVisible"
      :bottle-group="currentPrintBottle"
      :task-info="currentAssignment"
    />

    <!-- 关联样品箱对话框 -->
    <el-dialog title="关联样品箱" v-model="linkSampleBoxDialog" width="500px" append-to-body>
      <el-form :model="linkSampleBoxForm" label-width="100px">
        <el-form-item label="瓶组编号">
          <el-input v-model="linkSampleBoxForm.bottleGroup.bottleGroupCode" disabled />
        </el-form-item>
        <el-form-item label="选择样品箱">
          <el-select 
            v-model="linkSampleBoxForm.selectedSampleBoxId" 
            placeholder="请选择样品箱" 
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="box in linkSampleBoxForm.availableSampleBoxes"
              :key="box.id"
              :label="`${box.boxCode} - ${box.boxName || '无名称'}`"
              :value="box.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeLinkSampleBoxDialog">取消</el-button>
          <el-button type="primary" @click="confirmLinkSampleBox">确定关联</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加质控样对话框 -->
    <el-dialog title="添加质控样" v-model="qualityControlDialogVisible" width="500px" append-to-body>
      <el-form :model="qualityControlForm" :rules="qualityControlRules" ref="qualityControlFormRef" label-width="120px">
        <el-form-item label="原样品信息">
          <div style="color: #606266;">
            样品{{ qualityControlForm.sampleNumber }} - {{ qualityControlForm.pointName }}
          </div>
        </el-form-item>
        <el-form-item label="质控样类型" prop="qualityControlTypes">
          <el-checkbox-group v-model="qualityControlForm.qualityControlTypes">
            <el-checkbox label="parallel_sample">平行样</el-checkbox>
            <el-checkbox label="full_blank_sample">全程空白样</el-checkbox>
            <el-checkbox label="transport_blank_sample">运输空白样</el-checkbox>
            <el-checkbox label="equipment_blank_sample">设备清洗空白样</el-checkbox>
            <el-checkbox label="matrix_spike_sample">基体加标样</el-checkbox>
            <el-checkbox label="lab_parallel_sample">实验室平行样</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="qualityControlDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleCreateQualityControl" :loading="qualityControlCreating">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue';
import { getUserGroups, getAllGroups, getGroupDetail, updateGroupStatus } from "@/api/sampling/taskGroup";

import {
  getSampleRecordsByGroup,
  createQualityControlSamples,
  getSamplesWithQualityControlsByGroupId
} from "@/api/sampling/sampleRecord";
import {
  generateBottleGroups,
  getBottleGroupsByTask,
  getBottleGroupsBySample,
  updateBottleGroupStatus,
  getBottleGroupStatistics
} from "@/api/sampling/bottleGroup";

import {
  getAvailableSampleBoxesByTask,
  linkBottleGroupToSampleBox
} from "@/api/sampling/bottleGroup";
import { checkPermi } from "@/utils/permission";
import { useDict } from "@/utils/dict";
import useUserStore from '@/store/modules/user';
import {
  Box,
  Document,
  Clock,
  Check,
  Upload,
  Loading,
  CircleCheck,
  Printer
} from '@element-plus/icons-vue';
import PointInfoDialog from '@/components/sampling/PointInfoDialog.vue';
import SampleLabelPrint from '@/components/sampling/SampleLabelPrint.vue';

// 定义组件名称
defineOptions({
  name: "SamplingExecution"
});

// 获取当前实例
const { proxy } = getCurrentInstance();

// 字典数据
const { sampling_task_status, sample_record_status } = useDict('sampling_task_status', 'sample_record_status');
const dict = reactive({
  type: {
    sampling_task_status,
    sample_record_status
  }
});

// 响应式数据
const activeTab = ref('my-tasks');
const isAdmin = ref(false);
const loading = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const executionList = ref([]);
const title = ref("");
const open = ref(false);
const detailOpen = ref(false);
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  taskName: null,
  status: null
});
const taskDetail = ref({});

// 样品管理相关
const sampleManagementOpen = ref(false);
const currentAssignment = ref({});
const activeManagementTab = ref('samples');
const sampleRecords = ref([]);
const sampleLoading = ref(false);
const expandedSampleRows = ref([]); // 追踪展开的样品行
const sampleTableRef = ref(null); // 样品表格引用
const sampleStatistics = ref({
  totalCount: 0,
  pendingCount: 0,
  collectedCount: 0,
  submittedCount: 0,
  testingCount: 0,
  completedCount: 0
});

// 瓶组管理相关
const bottleGroups = ref([]);
const bottleLoading = ref(false);
const bottleGenerating = ref(false);
const selectedBottles = ref([]);
const bottleStatistics = ref({
  totalCount: 0,
  pendingCount: 0,
  collectedCount: 0,
  submittedCount: 0
});

// 瓶组关联样品箱相关
const linkSampleBoxDialog = ref(false);
const linkSampleBoxForm = reactive({
  bottleGroup: null,
  availableSampleBoxes: [],
  selectedSampleBoxId: null
});

// 点位信息相关
const currentGroupId = ref(null);

// 打印相关
const printDialogVisible = ref(false);
const currentPrintBottle = ref(null);

// 质控样相关
const qualityControlDialogVisible = ref(false);
const qualityControlCreating = ref(false);
const qualityControlFormRef = ref(null);
const qualityControlForm = reactive({
  originalSampleId: null,
  sampleNumber: '',
  pointName: '',
  qualityControlTypes: []
});
const qualityControlRules = reactive({
  qualityControlTypes: [
    { required: true, type: 'array', min: 1, message: '请至少选择一种质控样类型', trigger: 'change' }
  ]
});

// 计算属性：正常样品列表（非质控样）
const normalSamples = computed(() => {
  return sampleRecords.value.filter(sample => !sample.isQualityControl);
});

// 用户store
const userStore = useUserStore();

// 计算属性
const currentUserId = computed(() => {
  return userStore.id;
});

const hasSelectedBottles = computed(() => {
  return selectedBottles.value.length > 0;
});

// 方法定义
/** 检查权限 */
const checkPermissions = () => {
  // 检查用户是否具有assignment-execution:all权限
  isAdmin.value = checkPermi(['assignment-execution:all']);
};



/** Tab切换处理 */
const handleTabClick = (tab) => {
  activeTab.value = tab.props?.name || tab.paneName;
  getList();
};

/** 查询执行任务列表 */
const getList = () => {
  loading.value = true;

  if (activeTab.value === 'my-tasks') {
    getMyTasks();
  } else if (activeTab.value === 'all-tasks') {
    getAllTasks();
  }
};

/** 获取我的执行任务 */
const getMyTasks = () => {
  const userId = userStore.id;

  // 检查用户ID是否存在
  if (!userId) {
    console.error('用户ID不存在，请先登录');
    proxy.$modal.msgError('用户信息获取失败，请重新登录');
    loading.value = false;
    return;
  }

  getUserGroups(userId).then(response => {
    executionList.value = response.data || [];
    total.value = executionList.value.length;
    loading.value = false;
  }).catch(error => {
    console.error('获取我的执行任务列表失败:', error);
    proxy.$modal.msgError('获取任务列表失败');
    executionList.value = [];
    total.value = 0;
    loading.value = false;
  });
};

/** 获取所有执行任务（需要权限） */
const getAllTasks = () => {
  if (!isAdmin.value) {
    proxy.$modal.msgError('权限不足，无法查看所有执行任务');
    loading.value = false;
    return;
  }

  // 构建查询参数
  const query = {
    taskName: queryParams.taskName,
    status: queryParams.status,
    pageNum: queryParams.pageNum,
    pageSize: queryParams.pageSize
  };

  getAllGroups(query).then(response => {
    executionList.value = response.data || [];
    total.value = executionList.value.length;
    loading.value = false;
  }).catch(error => {
    console.error('获取所有执行任务列表失败:', error);
    proxy.$modal.msgError('获取任务列表失败');
    executionList.value = [];
    total.value = 0;
    loading.value = false;
  });
};

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryForm");
  handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

/** 详情按钮操作 */
const handleDetail = (row) => {
  const groupId = parseInt(row.id); // 确保ID是整数
  if (isNaN(groupId)) {
    proxy.$modal.msgError('无效的分组ID');
    return;
  }
  getGroupDetail(groupId).then(response => {
    // 将分组数据转换为原有的任务详情格式
    const groupData = response.data;
    taskDetail.value = {
      id: groupData.samplingTaskId,
      taskName: groupData.taskName || '未知任务',
      taskCode: groupData.taskCode || '未知编号',
      projectName: groupData.projectName || '未知项目',
      customerName: groupData.customerName || '未知客户',
      status: groupData.status,
      // 添加分组相关信息
      groupId: groupData.id,
      cycleNumber: groupData.cycleNumber,
      cycleType: groupData.cycleType,
      detectionCategory: groupData.detectionCategory,
      pointName: groupData.pointName,
      assignedUserIds: groupData.assignedUserIds,
      assignedUserNames: groupData.assignedUserNames,
      // 添加周期条目信息
      cycleItems: groupData.cycleItems || []
    };
    detailOpen.value = true;
  }).catch(error => {
    console.error('获取分组详情失败:', error);
    proxy.$modal.msgError('获取分组详情失败');
  });
};

/** 获取周期条目状态类型 */
const getCycleItemStatusType = (status) => {
  const statusMap = {
    0: 'warning',  // 未分配
    1: 'info',     // 已分配
    2: 'primary',  // 执行中
    3: 'success'   // 已完成
  };
  return statusMap[status] || 'info';
};

/** 获取周期条目状态标签 */
const getCycleItemStatusLabel = (status) => {
  const statusMap = {
    0: '未分配',
    1: '已分配',
    2: '执行中',
    3: '已完成'
  };
  return statusMap[status] || '未知状态';
};

/** 计算总样品数量 */
const getTotalSampleCount = (cycleItems) => {
  if (!cycleItems || cycleItems.length === 0) {
    return 0;
  }
  return cycleItems.reduce((total, item) => {
    return total + (item.sampleCount || 0);
  }, 0);
};

/** 开始执行按钮操作 */
const handleExecute = (row) => {
  proxy.$modal.confirm('确认开始执行该分组任务？').then(async () => {
    try {
      // 更新分组状态为执行中
      const groupId = parseInt(row.id);
      if (isNaN(groupId)) {
        proxy.$modal.msgError('无效的分组ID');
        return;
      }

      await updateGroupStatus(groupId, 1); // 1表示执行中

      // 样品记录和瓶组已在创建任务时生成，这里只需要更新状态
      proxy.$modal.msgSuccess("分组任务已开始执行");
      getList();
    } catch (error) {
      console.error('开始执行分组任务失败:', error);
      proxy.$modal.msgError('开始执行分组任务失败');
    }
  }).catch(() => {});
};

/** 完成任务按钮操作 */
const handleComplete = (row) => {
  proxy.$modal.confirm('确认完成该分组任务？').then(async () => {
    try {
      // 更新分组状态为已完成
      const groupId = parseInt(row.id);
      if (isNaN(groupId)) {
        proxy.$modal.msgError('无效的分组ID');
        return;
      }

      await updateGroupStatus(groupId, 2); // 2表示已完成
      proxy.$modal.msgSuccess("分组任务已完成");
      getList();
    } catch (error) {
      console.error('完成分组任务失败:', error);
      proxy.$modal.msgError('完成分组任务失败');
    }
  }).catch(() => {});
};

/** 采样管理按钮操作 */
const handleSamplingManagement = (row) => {
  const groupId = parseInt(row.id); // 确保ID是整数
  if (isNaN(groupId)) {
    proxy.$modal.msgError('无效的分组ID');
    return;
  }
  currentAssignment.value = row;
  currentGroupId.value = groupId;
  sampleManagementOpen.value = true;
  activeManagementTab.value = 'samples'; // 默认显示样品管理tab
  // 使用分组ID加载样品记录
  loadSampleRecordsForGroup(groupId);
  loadSampleStatisticsForGroup(groupId);
  // 加载瓶组信息
  loadBottleGroupsForTask(row.samplingTaskId);
  loadBottleStatisticsForTask(row.samplingTaskId);
};

/** 采样管理对话框关闭处理 */
const handleSampleManagementClose = () => {
  // 重置展开状态
  expandedSampleRows.value = [];
  // 清空样品记录中的瓶组数据以释放内存
  sampleRecords.value.forEach(record => {
    if (record.bottleGroups !== null) {
      record.bottleGroups = null;
      record.bottleGroupsLoading = false;
    }
  });
};



/** 加载分组样品记录列表 */
const loadSampleRecordsForGroup = async (groupId, forceReload = false) => {
  try {
    sampleLoading.value = true;
    // 使用包含质控样的接口
    const response = await getSamplesWithQualityControlsByGroupId(groupId);

    // 如果不是强制重新加载，尝试保留已有的瓶组数据
    const existingRecords = forceReload ? [] : sampleRecords.value;
    const existingBottleData = {};

    // 保存现有的瓶组数据
    existingRecords.forEach(record => {
      if (record.bottleGroups !== null) {
        existingBottleData[record.id] = {
          bottleGroups: record.bottleGroups,
          bottleGroupsLoading: record.bottleGroupsLoading
        };
      }
    });

    // 初始化样品记录并添加瓶组相关属性
    const records = (response.data || []).map(record => {
      const existing = existingBottleData[record.id];
      return {
        ...record,
        // 如果有现有数据则保留，否则初始化为null
        bottleGroups: existing ? existing.bottleGroups : null,
        bottleGroupsLoading: existing ? existing.bottleGroupsLoading : false
      };
    });

    sampleRecords.value = records;
  } catch (error) {
    console.error('获取分组样品记录失败:', error);
    sampleRecords.value = [];
    proxy.$modal.msgError('获取样品记录失败');
  } finally {
    sampleLoading.value = false;
  }
};



/** 加载分组样品统计信息 */
const loadSampleStatisticsForGroup = async (groupId) => {
  try {
    // 暂时使用简单的统计计算，后续可以添加专门的分组统计接口
    const response = await getSampleRecordsByGroup(groupId);
    const records = response.data || [];

    // 计算统计信息
    const totalCount = records.length;
    const collectedCount = records.filter(r => r.status === 1).length;
    const pendingCount = records.filter(r => r.status === 0).length;
    const submittedCount = records.filter(r => r.status === 2).length;
    const testingCount = records.filter(r => r.status === 3).length;
    const completedCount = records.filter(r => r.status === 4).length;

    sampleStatistics.value = {
      totalCount,
      pendingCount,
      collectedCount,
      submittedCount,
      testingCount,
      completedCount
    };
  } catch (error) {
    console.error('获取分组样品统计失败:', error);
    sampleStatistics.value = {
      totalCount: 0,
      pendingCount: 0,
      collectedCount: 0,
      submittedCount: 0,
      testingCount: 0,
      completedCount: 0
    };
  }
};



/** 刷新样品列表 */
const refreshSampleList = () => {
  const groupId = parseInt(currentAssignment.value.id);
  if (!isNaN(groupId)) {
    loadSampleRecordsForGroup(groupId, true); // 强制重新加载
    loadSampleStatisticsForGroup(groupId);
  }
};

/** 显示添加质控样对话框 */
const showAddQualityControlDialog = () => {
  proxy.$modal.msgWarning('请点击具体样品行的"添加质控样"按钮');
};

/** 为指定样品显示添加质控样对话框 */
const showAddQualityControlForSample = (sample) => {
  // 设置表单数据（原样品信息仅用于显示，不需要用户输入）
  qualityControlForm.originalSampleId = sample.id;
  qualityControlForm.sampleNumber = sample.sampleNumber;
  qualityControlForm.pointName = sample.pointName || '';
  qualityControlForm.qualityControlTypes = [];

  qualityControlDialogVisible.value = true;
};

/** 创建质控样 */
const handleCreateQualityControl = async () => {
  try {
    // 表单验证
    const valid = await qualityControlFormRef.value.validate();
    if (!valid) return;

    if (!qualityControlForm.originalSampleId) {
      proxy.$modal.msgWarning('请先选择原样品');
      return;
    }

    qualityControlCreating.value = true;

    const createData = {
      originalSampleId: qualityControlForm.originalSampleId,
      qualityControlTypes: qualityControlForm.qualityControlTypes
    };

    const response = await createQualityControlSamples(createData);

    if (response.code === 200) {
      proxy.$modal.msgSuccess(`质控样创建成功，共创建 ${response.data.length} 个质控样`);
      qualityControlDialogVisible.value = false;

      // 刷新样品列表
      await loadSampleRecordsForGroup(currentGroupId.value, true);
      await loadSampleStatisticsForGroup(currentGroupId.value);
    } else {
      proxy.$modal.msgError(response.msg || '创建质控样失败');
    }

  } catch (error) {
    console.error('创建质控样失败:', error);
    proxy.$modal.msgError('创建质控样失败');
  } finally {
    qualityControlCreating.value = false;
  }
};

/** 获取质控样类型标签 */
const getQualityControlTypeLabel = (type) => {
  const typeMap = {
    'parallel_sample': '平行样',
    'full_blank_sample': '全程空白样',
    'transport_blank_sample': '运输空白样',
    'equipment_blank_sample': '设备清洗空白样',
    'matrix_spike_sample': '基体加标样',
    'lab_parallel_sample': '实验室平行样'
  };
  return typeMap[type] || type;
};

/** 获取样品状态类型 */
const getSampleStatusType = (status) => {
  const statusMap = {
    0: 'warning',  // 待采集
    1: 'info',     // 采集中
    2: 'success',  // 已采集
    3: 'primary'   // 已提交
  };
  return statusMap[status] || 'info';
};

/** 获取样品状态标签 */
const getSampleStatusLabel = (status) => {
  const statusMap = {
    0: '待采集',
    1: '采集中',
    2: '已采集',
    3: '已提交'
  };
  return statusMap[status] || '未知状态';
};



// 移除批量采集和送检方法，样品状态由瓶组状态自动计算



/** 样品展开时加载瓶组信息 */
const handleSampleExpand = async (row, expandedRows) => {
  // 更新展开状态追踪
  const newExpandedRows = expandedRows.map(r => r.id);
  expandedSampleRows.value = newExpandedRows;

  // 检查是否为展开操作且需要加载瓶组信息
  const isExpanding = expandedRows.includes(row);
  const needsLoading = row.bottleGroups === null && !row.bottleGroupsLoading;

  if (isExpanding && needsLoading) {
    // 找到当前行在sampleRecords中的索引
    const rowIndex = sampleRecords.value.findIndex(record => record.id === row.id);
    if (rowIndex === -1) {
      console.error('找不到对应的样品记录');
      return;
    }

    try {
      // 设置加载状态
      sampleRecords.value[rowIndex].bottleGroupsLoading = true;

      // 获取该样品关联的瓶组信息
      const response = await getBottleGroupsBySample(row.id);

      if (response.code === 200) {
        // 将瓶组信息添加到样品记录中
        sampleRecords.value[rowIndex].bottleGroups = response.data || [];

        // 确保展开状态正确更新
        await nextTick();

        // 如果表格引用存在，强制更新展开状态
        if (sampleTableRef.value) {
          sampleTableRef.value.toggleRowExpansion(row, true);
        }
      } else {
        sampleRecords.value[rowIndex].bottleGroups = [];
      }

    } catch (error) {
      console.error('加载瓶组信息失败:', error);
      proxy.$modal.msgError('加载瓶组信息失败');
      sampleRecords.value[rowIndex].bottleGroups = [];
    } finally {
      // 清除加载状态
      sampleRecords.value[rowIndex].bottleGroupsLoading = false;
    }
  }
};



// 瓶组相关方法
/** 加载任务的瓶组列表 */
const loadBottleGroupsForTask = async (taskId) => {
  try {
    bottleLoading.value = true;
    const response = await getBottleGroupsByTask(taskId);
    bottleGroups.value = response.data || [];
  } catch (error) {
    console.error('获取瓶组列表失败:', error);
    proxy.$modal.msgError('获取瓶组列表失败');
    bottleGroups.value = [];
  } finally {
    bottleLoading.value = false;
  }
};

/** 加载任务的瓶组统计信息 */
const loadBottleStatisticsForTask = async (taskId) => {
  try {
    const response = await getBottleGroupStatistics(taskId);
    bottleStatistics.value = response.data || {
      totalCount: 0,
      pendingCount: 0,
      collectedCount: 0,
      submittedCount: 0
    };
  } catch (error) {
    console.error('获取瓶组统计失败:', error);
    bottleStatistics.value = {
      totalCount: 0,
      pendingCount: 0,
      collectedCount: 0,
      submittedCount: 0
    };
  }
};

/** 生成瓶组 */
const handleGenerateBottleGroups = async () => {
  try {
    bottleGenerating.value = true;
    const taskId = currentAssignment.value.samplingTaskId;
    const response = await generateBottleGroups(taskId);
    proxy.$modal.msgSuccess(response.msg || '瓶组生成成功');
    await loadBottleGroupsForTask(taskId);
    await loadBottleStatisticsForTask(taskId);
  } catch (error) {
    console.error('生成瓶组失败:', error);
    proxy.$modal.msgError('生成瓶组失败');
  } finally {
    bottleGenerating.value = false;
  }
};

/** 刷新瓶组列表 */
const refreshBottleList = () => {
  const taskId = currentAssignment.value.samplingTaskId;
  if (taskId) {
    loadBottleGroupsForTask(taskId);
    loadBottleStatisticsForTask(taskId);
  }
};

/** 瓶组选择变化 */
const handleBottleSelectionChange = (selection) => {
  selectedBottles.value = selection;
};

/** 获取瓶组状态类型 */
const getBottleStatusType = (status) => {
  const statusMap = {
    0: 'info',     // 采样 - 蓝色
    1: 'warning',  // 装箱 - 橙色
    2: 'primary',  // 流转 - 紫色
    3: 'success'   // 完成 - 绿色
  };
  return statusMap[status] || 'info';
};

/** 获取瓶组状态标签 */
const getBottleStatusLabel = (status) => {
  const statusMap = {
    0: '采样',
    1: '装箱',
    2: '流转',
    3: '完成'
  };
  return statusMap[status] || '未知';
};

/** 获取下一个状态的标签 */
const getNextStatusLabel = (currentStatus) => {
  const nextStatusMap = {
    0: '装箱',  // 采样 -> 装箱
    1: '流转',  // 装箱 -> 流转
    2: '完成'   // 流转 -> 完成
  };
  return nextStatusMap[currentStatus] || '完成';
};

/** 瓶组状态更新 */
const handleBottleStatusUpdate = async (bottle, newStatus) => {
  try {
    // 如果是更新到装箱状态(1)且当前状态是采样状态(0)
    if (newStatus === 1 && bottle.status === 0) {
      // 弹出对话框选择样品箱
      try {
        // 设置当前瓶组
        linkSampleBoxForm.bottleGroup = bottle;
        
        // 获取该任务下新建状态的样品箱
        const response = await getAvailableSampleBoxesByTask(currentAssignment.value.samplingTaskId);
        linkSampleBoxForm.availableSampleBoxes = response.data || [];
        
        // 如果没有可用的样品箱，提示用户
        if (linkSampleBoxForm.availableSampleBoxes.length === 0) {
          proxy.$modal.msgWarning('当前任务没有新建状态的样品箱可关联');
          return;
        }
        
        // 显示对话框
        linkSampleBoxDialog.value = true;
      } catch (error) {
        console.error('获取可用样品箱列表失败:', error);
        proxy.$modal.msgError('获取可用样品箱列表失败');
      }
    } else {
      // 对于其他状态流转，直接更新
      const statusNames = {0: '采样', 1: '装箱', 2: '流转', 3: '完成'};
      const currentStatusName = statusNames[bottle.status];
      const newStatusName = statusNames[newStatus];

      await proxy.$modal.confirm(
        `确认将瓶组 ${bottle.bottleGroupCode} 的状态从"${currentStatusName}"更新为"${newStatusName}"吗？`,
        '状态更新确认'
      );

      await updateBottleGroupStatus(bottle.id, newStatus);
      proxy.$modal.msgSuccess(`瓶组状态已更新为"${newStatusName}"`);

      // 刷新瓶组数据
      await loadBottleGroupsForTask(currentAssignment.value.samplingTaskId);

      // 刷新样品记录中的瓶组信息
      await loadSampleRecordsForGroup(currentGroupId.value, true);
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('更新瓶组状态失败:', error);
      proxy.$modal.msgError('更新瓶组状态失败');
    }
  }
};

/** 瓶组详情 */
const handleBottleDetail = (row) => {
  // TODO: 实现瓶组详情功能
  proxy.$modal.msgInfo('瓶组详情功能待实现');
};


/** 瓶组送检 */
const handleBottleSubmit = async (row) => {
  try {
    await updateBottleGroupStatus(row.id, 2);
    proxy.$modal.msgSuccess('瓶组送检成功');
    refreshBottleList();
  } catch (error) {
    console.error('瓶组送检失败:', error);
    proxy.$modal.msgError('瓶组送检失败');
  }
};



/** 批量瓶组送检 */
const handleBatchBottleSubmit = async () => {
  if (selectedBottles.value.length === 0) {
    proxy.$modal.msgWarning('请选择要送检的瓶组');
    return;
  }

  try {
    const promises = selectedBottles.value
      .filter(bottle => bottle.status === 1)
      .map(bottle => updateBottleGroupStatus(bottle.id, 2));

    await Promise.all(promises);
    proxy.$modal.msgSuccess('批量送检成功');
    refreshBottleList();
  } catch (error) {
    console.error('批量送检失败:', error);
    proxy.$modal.msgError('批量送检失败');
  }
};

/** 瓶组打印 */
const handleBottlePrint = (bottle) => {
  // 找到当前瓶组所属的样品记录
  let sampleIndex = 0;
  let totalSamples = sampleRecords.value.length;

  // 遍历样品记录，找到包含当前瓶组的样品
  for (let i = 0; i < sampleRecords.value.length; i++) {
    const record = sampleRecords.value[i];
    if (record.bottleGroups && record.bottleGroups.some(b => b.id === bottle.id)) {
      sampleIndex = i + 1; // 样品序号从1开始
      break;
    }
  }

  // 为瓶组添加样品序号信息
  const bottleWithIndex = {
    ...bottle,
    sortOrder: sampleIndex,
    totalCount: totalSamples
  };

  // 为任务信息添加样品总数
  const taskInfoWithCount = {
    ...currentAssignment.value,
    totalSamples: totalSamples,
    sampleCount: totalSamples
  };

  currentPrintBottle.value = bottleWithIndex;
  // 更新任务信息
  currentAssignment.value = taskInfoWithCount;
  printDialogVisible.value = true;
};

/** 打开关联样品箱对话框 */
const openLinkSampleBoxDialog = async (bottle) => {
  try {
    // 设置当前瓶组
    linkSampleBoxForm.bottleGroup = bottle;
    
    // 获取该任务下新建状态的样品箱
    const response = await getAvailableSampleBoxesByTask(currentAssignment.value.samplingTaskId);
    linkSampleBoxForm.availableSampleBoxes = response.data || [];
    
    // 显示对话框
    linkSampleBoxDialog.value = true;
  } catch (error) {
    console.error('获取可用样品箱列表失败:', error);
    proxy.$modal.msgError('获取可用样品箱列表失败');
  }
};

/** 关闭关联样品箱对话框 */
const closeLinkSampleBoxDialog = () => {
  linkSampleBoxDialog.value = false;
  linkSampleBoxForm.selectedSampleBoxId = null;
  linkSampleBoxForm.bottleGroup = null;
  linkSampleBoxForm.availableSampleBoxes = [];
};

/** 确认关联样品箱 */
const confirmLinkSampleBox = async () => {
  if (!linkSampleBoxForm.selectedSampleBoxId) {
    proxy.$modal.msgWarning('请选择样品箱');
    return;
  }
  
  try {
    await linkBottleGroupToSampleBox(
      linkSampleBoxForm.bottleGroup.id, 
      linkSampleBoxForm.selectedSampleBoxId
    );
    
    proxy.$modal.msgSuccess('关联样品箱成功');
    
    // 更新瓶组状态到装箱（1）
    await updateBottleGroupStatus(linkSampleBoxForm.bottleGroup.id, 1);
    proxy.$modal.msgSuccess('瓶组状态已更新为装箱');
    
    closeLinkSampleBoxDialog();
    
    // 刷新瓶组列表
    refreshBottleList();
  } catch (error) {
    console.error('关联样品箱失败:', error);
    proxy.$modal.msgError('关联样品箱失败');
  }
};

// 组件挂载时执行
 onMounted(() => {
   checkPermissions();
   getList();
 });
</script>

<style scoped>
.app-container {
  padding: 20px;
}



/* 按钮组样式优化 */
.el-row.mb8 .el-button {
  margin-right: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.el-row.mb8 .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 表格操作列样式 */
.small-padding .cell {
  padding-left: 8px;
  padding-right: 8px;
}

.fixed-width .el-button {
  margin-right: 8px;
  margin-bottom: 4px;
}

/* 表格行悬停效果 */
.el-table tbody tr:hover > td {
  background-color: var(--el-fill-color-light) !important;
}

/* 状态标签样式增强 */
.dict-tag {
  font-weight: 500;
  border-radius: 12px;
  padding: 4px 8px;
}

/* Tab样式 */
.execution-tabs {
  margin-bottom: 20px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  box-shadow: 0 2px 4px var(--el-box-shadow-light);
  padding: 0 16px;
  border: 1px solid var(--el-border-color-light);
}

.execution-tabs .el-tabs__header {
  margin-bottom: 0;
  border-bottom: 2px solid var(--el-border-color);
}

.execution-tabs .el-tabs__nav-wrap::after {
  height: 2px;
  background-color: var(--el-border-color);
}

.execution-tabs .el-tabs__active-bar {
  background-color: var(--el-color-primary);
}

.execution-tabs .el-tabs__item {
  font-weight: 500;
  color: var(--el-text-color-regular);
  transition: all 0.3s ease;
}

.execution-tabs .el-tabs__item:hover {
  color: var(--el-color-primary);
}

.execution-tabs .el-tabs__item.is-active {
  color: var(--el-color-primary);
  font-weight: 600;
}

/* 对话框样式优化 */
.el-dialog__header {
  background-color: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color);
}

.el-dialog__title {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.detail-dialog .el-dialog__body {
  padding: 20px;
  height: 80vh;
  overflow: hidden;
}

.detail-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-descriptions {
  margin-bottom: 24px;
  flex-shrink: 0;
}

.el-descriptions {
  margin-bottom: 20px;
}

.el-descriptions__label {
  font-weight: 600;
  color: var(--el-text-color-regular);
}

/* 分组信息样式 */
.group-info {
  background-color: var(--el-bg-color-page);
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid var(--el-color-primary);
  margin-bottom: 16px;
  border: 1px solid var(--el-border-color-light);
}

.group-info p {
  margin: 4px 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.group-info strong {
  color: var(--el-text-color-primary);
}

/* 周期项目表格样式 */
.cycle-items-section {
  margin-top: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-title {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
  font-weight: 600;
  font-size: 16px;
  border-bottom: 2px solid var(--el-color-primary);
  padding-bottom: 8px;
  flex-shrink: 0;
}

.cycle-items-table {
  border-radius: 6px;
  overflow: hidden;
  flex: 1;
}

.cycle-items-table .el-table__body-wrapper {
  max-height: calc(80vh - 300px);
  overflow-y: auto;
}

/* 卡片样式增强 */
.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.el-card__header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #e9ecef;
  font-weight: 600;
}

/* 样品管理对话框样式 */
.sample-management-dialog .el-dialog__body {
  padding: 20px;
  height: 80vh;
  overflow: hidden;
}

.sample-management-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.task-info {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.sample-operations {
  flex-shrink: 0;
}

.sample-operations .el-button {
  margin-right: 12px;
}

.sample-statistics {
  flex-shrink: 0;
  background-color: var(--el-bg-color-page);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--el-border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sample-statistics .el-statistic {
  text-align: center;
  padding: 12px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
}

.sample-statistics .el-statistic:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.sample-statistics .el-statistic__content {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-color-primary);
}

.sample-statistics .el-statistic__head {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin-bottom: 8px;
  font-weight: 500;
}

/* 统计卡片样式 */
.statistic-card {
  padding: 12px;
  background-color: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statistic-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
  border-color: var(--el-color-primary);
}

.statistic-card.total:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.statistic-card.pending:hover {
  border-color: #e6a23c;
  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.2);
}

.statistic-card.collected:hover {
  border-color: #67c23a;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.2);
}

.statistic-card.submitted:hover {
  border-color: #909399;
  box-shadow: 0 4px 12px rgba(144, 147, 153, 0.2);
}

.statistic-card.testing:hover {
  border-color: #f56c6c;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.2);
}

.statistic-card.completed:hover {
  border-color: #67c23a;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.2);
}

.statistic-card .el-statistic {
  background: none !important;
  border: none !important;
  padding: 0 !important;
}

.statistic-card .el-statistic:hover {
  transform: none !important;
  box-shadow: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .search-form {
    padding: 12px;
  }

  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 12px;
  }

  .el-form-item .el-input,
  .el-form-item .el-select {
    width: 100% !important;
  }

  .el-table {
    font-size: 12px;
  }

  .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .execution-tabs {
    padding: 0 8px;
  }

  .el-table-column {
    min-width: 80px;
  }

  .sample-management-dialog .el-dialog {
    width: 98% !important;
  }

  .sample-operations .el-button {
    margin-bottom: 8px;
    width: 100%;
  }
}

/* 瓶组信息样式 */
.sample-bottle-groups {
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.bottle-groups-container {
  max-height: 300px;
  overflow-y: auto;
}

.bottle-info-item {
  margin-bottom: 6px;
  display: flex;
  align-items: flex-start;
  font-size: 12px;
  line-height: 1.4;
}

.bottle-info-item .label {
  font-weight: 600;
  color: var(--el-text-color-regular);
  min-width: 50px;
  margin-right: 6px;
  flex-shrink: 0;
  font-size: 12px;
}

.bottle-info-item .value {
  color: var(--el-text-color-primary);
  flex: 1;
  word-break: break-word;
  font-size: 12px;
}

.no-bottle-groups {
  padding: 40px 20px;
  text-align: center;
}

/* 瓶组卡片样式 */
.bottle-groups-container .el-card {
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.bottle-groups-container .el-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 12px var(--el-box-shadow);
}

.bottle-groups-container .el-card__header {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-bottom: 1px solid #e4e7ed;
  padding: 12px 16px;
}

.bottle-groups-container .el-card__body {
  padding: 16px;
}

/* 展开列样式 */
.el-table .el-table__expand-column .cell {
  text-align: center;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

/* 滚动条样式 */
.bottle-groups-container::-webkit-scrollbar {
  width: 6px;
}

.bottle-groups-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.bottle-groups-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.bottle-groups-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 瓶组加载状态样式 */
.bottle-groups-loading {
  padding: 20px;
}

.bottle-groups-loading .el-skeleton {
  background-color: var(--el-bg-color);
  border-radius: 8px;
  padding: 16px;
}

/* 表格中的合同信息样式 */
.contract-info-table {
  .contract-name {
    display: flex;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;
    color: var(--el-text-color-primary);

    .el-icon {
      margin-right: 4px;
      color: var(--el-color-primary);
    }
  }

  .contract-number {
    font-size: 11px;
    color: var(--el-text-color-regular);
  }

  .no-contract {
    font-size: 11px;
  }
}

/* 表格中的方案详情样式 */
.scheme-details-table {
  .detail-row {
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }

    .el-tag {
      font-size: 11px;
      padding: 2px 6px;
      height: auto;
      line-height: 1.2;
    }
  }

  .no-scheme {
    font-size: 11px;
  }
}

/* 任务详情中的方案详情样式 */
.scheme-details-inline {
  .el-tag {
    margin-bottom: 4px;
  }
}

/* 空状态样式优化 */
.no-bottle-groups {
  padding: 40px 20px;
  text-align: center;
  background-color: var(--el-bg-color);
  border-radius: 8px;
  border: 1px dashed var(--el-border-color);
}

.no-bottle-groups .el-empty {
  padding: 20px;
}

.no-bottle-groups .el-empty__description {
  color: var(--el-text-color-regular);
}

/* 采样管理tab页样式 */
.management-tabs {
  margin-top: 20px;
}

.management-tabs .el-tabs__content {
  padding: 20px 0;
}

.point-info-content {
  max-height: 60vh;
  overflow-y: auto;
}
</style>

<script>
export default {
  components: {
    PointInfoDialog
  }
}
</script>
