<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务ID" prop="samplingTaskId">
        <el-input
          v-model="queryParams.samplingTaskId"
          placeholder="请输入采样任务ID"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="样品箱编号" prop="boxCode">
        <el-input
          v-model="queryParams.boxCode"
          placeholder="请输入样品箱编号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="新建" value="0" />
          <el-option label="已封箱" value="1" />
          <el-option label="已发送" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['sampling:sampleBox:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['sampling:sampleBox:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['sampling:sampleBox:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="sampleBoxList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="样品箱ID" align="center" prop="id" />
      <el-table-column label="样品箱编号" align="center" prop="boxCode" />
      <el-table-column label="任务名称" align="center" prop="taskName" />
      <el-table-column label="任务编号" align="center" prop="taskCode" />
      <el-table-column label="样品数量" align="center" prop="sampleCount" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="statusOptions" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="是否封箱" align="center" prop="isSealed">
        <template #default="scope">
          <el-tag :type="scope.row.isSealed ? 'success' : 'info'">
            {{ scope.row.isSealed ? '已封箱' : '未封箱' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['sampling:sampleBox:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['sampling:sampleBox:edit']" v-if="!scope.row.isSealed">修改</el-button>
          <el-button link type="success" icon="Lock" @click="handleSeal(scope.row)" v-hasPermi="['sampling:sampleBox:seal']" v-if="!scope.row.isSealed && scope.row.sampleCount > 0">封箱</el-button>
          <el-button link type="danger" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['sampling:sampleBox:remove']" v-if="!scope.row.isSealed">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改样品箱对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="sampleBoxRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="采样任务" prop="samplingTaskId" v-if="!isEdit">
          <el-select v-model="form.samplingTaskId" placeholder="请选择采样任务" style="width: 100%">
            <el-option
              v-for="task in incompleteTasks"
              :key="task.id"
              :label="`${task.taskCode} - ${task.taskName}`"
              :value="task.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="采样任务" v-else>
          <el-input v-model="form.taskName" disabled />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 样品箱详情对话框 -->
    <el-dialog title="样品箱详情" v-model="detailOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="样品箱编号">{{ detailForm.boxCode }}</el-descriptions-item>
        <el-descriptions-item label="任务名称">{{ detailForm.taskName }}</el-descriptions-item>
        <el-descriptions-item label="任务编号">{{ detailForm.taskCode }}</el-descriptions-item>
        <el-descriptions-item label="样品数量">{{ detailForm.sampleCount }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="statusOptions" :value="detailForm.status"/>
        </el-descriptions-item>
        <el-descriptions-item label="是否封箱">
          <el-tag :type="detailForm.isSealed ? 'success' : 'info'">
            {{ detailForm.isSealed ? '已封箱' : '未封箱' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="封箱时间">
          {{ detailForm.sealedTime ? parseTime(detailForm.sealedTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建人">{{ detailForm.createByName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ parseTime(detailForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ detailForm.description || '-' }}</el-descriptions-item>
      </el-descriptions>
      
      <el-divider content-position="left">样品列表</el-divider>
      <el-table :data="detailForm.samples" style="width: 100%">
        <el-table-column prop="sampleNumber" label="样品编号" />
        <el-table-column prop="sampleType" label="样品类型" />
        <el-table-column prop="pointName" label="点位名称" />
        <el-table-column prop="detectionCategory" label="检测类别" />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <dict-tag :options="sampleStatusOptions" :value="scope.row.status"/>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { listSampleBox, getSampleBox, delSampleBox, addSampleBox, updateSampleBox, sealSampleBox } from "@/api/sampling/sampleBox";
import { getSampleRecordsByGroup } from "@/api/sampling/sampleRecord";
import { getIncompleteSamplingTasks } from "@/api/sampling/samplingTask";

export default {
  name: "SampleBox",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 样品箱表格数据
      sampleBoxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情弹出层
      detailOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        samplingTaskId: null,
        boxCode: null,
        status: null,
        isSealed: null
      },
      // 表单参数
      form: {},
      // 是否为编辑模式
      isEdit: false,
      // 详情表单参数
      detailForm: {},
      // 可用样品列表
      availableSamples: [],
      // 未完成任务列表
      incompleteTasks: [],
      // 表单校验
      rules: {
        samplingTaskId: [
          { required: true, message: "请选择采样任务", trigger: "change" }
        ]
      },
      // 状态选项
      statusOptions: [
        { label: "新建", value: "0" },
        { label: "已封箱", value: "1" },
        { label: "已发送", value: "2" }
      ],
      // 样品状态选项
      sampleStatusOptions: [
        { label: "待采集", value: "0" },
        { label: "已采集", value: "1" },
        { label: "已送检", value: "2" },
        { label: "检测中", value: "3" },
        { label: "已完成", value: "4" }
      ]
    };
  },
  created() {
    this.getList();
    this.loadIncompleteTasks();
  },
  methods: {
    /** 查询样品箱列表 */
    getList() {
      this.loading = true;
      listSampleBox(this.queryParams).then(response => {
        this.sampleBoxList = response.data.rows;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.isEdit = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        samplingTaskId: null,
        description: null
      };
      this.resetForm("sampleBoxRef");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryRef");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.isEdit = false;
      this.open = true;
      this.title = "添加样品箱";
      this.loadAvailableSamples();
      this.loadIncompleteTasks();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.isEdit = true;
      const id = row.id || this.ids[0];
      getSampleBox(id).then(response => {
        // 在编辑模式下，显示采样任务名称（不可编辑）和描述（可编辑）
        this.form = {
          id: response.data.id,
          taskName: `${response.data.taskCode} - ${response.data.taskName}`,  // 显示任务编号和名称
          description: response.data.description
        };
        this.open = true;
        this.title = "修改样品箱";
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      const id = row.id;
      getSampleBox(id).then(response => {
        this.detailForm = response.data;
        this.detailOpen = true;
      });
    },
    /** 封箱按钮操作 */
    handleSeal(row) {
      const id = row.id;
      this.$modal.confirm('是否确认封箱样品箱编号为"' + row.boxCode + '"的数据项？').then(function() {
        return sealSampleBox(id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("封箱成功");
      }).catch(() => {});
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["sampleBoxRef"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            // 修改操作：只传递id和description
            const updateData = {
              description: this.form.description
            };
            updateSampleBox(this.form.id, updateData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            // 创建操作：传递完整表单数据，除了id
            const createData = {
              samplingTaskId: this.form.samplingTaskId,
              description: this.form.description
            };
            addSampleBox(createData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除样品箱编号为"' + (row.boxCode || this.sampleBoxList.filter(item => this.ids.includes(item.id)).map(item => item.boxCode).join('", "')) + '"的数据项？').then(function() {
        return delSampleBox(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 加载可用样品列表 */
    loadAvailableSamples() {
      // 这里需要根据任务ID加载可用的样品记录
      // 暂时使用空数组，实际使用时需要调用相应的API
      this.availableSamples = [];

      // 如果有任务ID，则加载该任务的样品记录
      if (this.form.samplingTaskId) {
        // 这里应该调用获取任务样品记录的API
        // getSampleRecordsByTask(this.form.samplingTaskId).then(response => {
        //   this.availableSamples = response.data;
        // });
      }
    },
    /** 加载未完成任务列表 */
    async loadIncompleteTasks() {
      try {
        const response = await getIncompleteSamplingTasks();
        this.incompleteTasks = response.data;
      } catch (error) {
        console.error("加载未完成任务失败:", error);
        this.$message.error("加载未完成任务失败");
      }
    }
  }
};
</script>
