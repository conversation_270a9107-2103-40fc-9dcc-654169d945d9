<template>
  <div class="contract-payment-management">
    <!-- 汇总信息 -->
    <el-card class="summary-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span style="font-weight: bold; color: #409EFF;">回款汇总</span>
        </div>
      </template>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="合同金额" :value="summaryData.contractAmount" :precision="2" prefix="¥" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="已开票金额" :value="summaryData.totalInvoiceAmount" :precision="2" prefix="¥" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="已回款金额" :value="summaryData.totalPaymentAmount" :precision="2" prefix="¥" />
        </el-col>
        <el-col :span="6">
          <el-statistic 
            title="待回款金额" 
            :value="summaryData.pendingPaymentAmount" 
            :precision="2" 
            prefix="¥"
            :value-style="{ color: summaryData.pendingPaymentAmount > 0 ? '#f56c6c' : '#67c23a' }"
          />
        </el-col>
      </el-row>
    </el-card>

    <!-- 开票记录 -->
    <el-card class="mt-4" shadow="never">
      <template #header>
        <div class="card-header">
          <span style="font-weight: bold; color: #409EFF;">开票记录</span>
          <el-button 
            v-if="!readonly"
            type="primary" 
            size="small" 
            @click="showAddInvoiceDialog"
          >
            新增开票
          </el-button>
        </div>
        
      </template>
      
      <el-table :data="invoiceList" style="width: 100%" size="small">
        <el-table-column prop="invoiceNumber" label="发票号码" width="150" />
        <el-table-column prop="invoiceAmount" label="开票金额" width="120">
          <template #default="scope">
            ¥{{ Number(scope.row.invoiceAmount).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="invoiceDate" label="开票日期" width="120" />
        <el-table-column prop="invoiceType" label="发票类型" width="100" />
        <el-table-column prop="taxRate" label="税率" width="80">
          <template #default="scope">
            {{ scope.row.taxRate }}%
          </template>
        </el-table-column>
        <el-table-column prop="taxAmount" label="税额" width="100">
          <template #default="scope">
            ¥{{ Number(scope.row.taxAmount || 0).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="createBy" label="创建人" width="100" />
        <el-table-column prop="remark" label="备注" min-width="150" />
        <el-table-column v-if="!readonly" label="操作" width="100">
          <template #default="scope">
            <el-button type="danger" size="small" @click="deleteInvoice(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 回款记录 -->
    <el-card class="mt-4" shadow="never">
      <template #header>
        <div class="card-header">
          <span style="font-weight: bold; color: #409EFF;">回款记录</span>
          <el-button 
            v-if="!readonly" 
            type="primary" 
            size="small" 
            @click="showAddPaymentDialog"
          >
            新增回款
          </el-button>
        </div>
      </template>
      
      <el-table :data="paymentList" style="width: 100%" size="small">
        <el-table-column prop="paymentAmount" label="回款金额" width="120">
          <template #default="scope">
            ¥{{ Number(scope.row.paymentAmount).toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="paymentDate" label="回款日期" width="120" />
        <el-table-column prop="paymentMethod" label="回款方式" width="100" />
        <el-table-column prop="paymentAccount" label="回款账户" width="150" />
        <el-table-column prop="bankInfo" label="银行信息" min-width="150" />
        <el-table-column prop="createBy" label="创建人" width="100" />
        <el-table-column prop="remark" label="备注" min-width="150" />
        <el-table-column v-if="!readonly" label="操作" width="100">
          <template #default="scope">
            <el-button type="danger" size="small" @click="deletePayment(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增开票对话框 -->
    <el-dialog v-model="invoiceDialogVisible" title="新增开票记录" width="600px">
      <el-form ref="invoiceFormRef" :model="invoiceForm" :rules="invoiceRules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发票号码" prop="invoiceNumber">
              <el-input v-model="invoiceForm.invoiceNumber" placeholder="请输入发票号码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开票金额" prop="invoiceAmount">
              <el-input-number
                v-model="invoiceForm.invoiceAmount"
                :precision="2"
                :min="0"
                placeholder="开票金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开票日期" prop="invoiceDate">
              <el-date-picker
                v-model="invoiceForm.invoiceDate"
                type="date"
                placeholder="选择开票日期"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发票类型" prop="invoiceType">
              <el-select v-model="invoiceForm.invoiceType" placeholder="请选择发票类型" style="width: 100%">
                <el-option label="增值税专用发票" value="增值税专用发票" />
                <el-option label="增值税普通发票" value="增值税普通发票" />
                <el-option label="电子发票" value="电子发票" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="税率(%)" prop="taxRate">
              <el-input-number
                v-model="invoiceForm.taxRate"
                :precision="2"
                :min="0"
                :max="100"
                placeholder="税率"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="税额" prop="taxAmount">
              <el-input-number
                v-model="invoiceForm.taxAmount"
                :precision="2"
                :min="0"
                placeholder="税额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注">
          <el-input v-model="invoiceForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="invoiceDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitInvoice" :loading="invoiceSubmitting">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增回款对话框 -->
    <el-dialog v-model="paymentDialogVisible" title="新增回款记录" width="600px">
      <el-form ref="paymentFormRef" :model="paymentForm" :rules="paymentRules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="回款金额" prop="paymentAmount">
              <el-input-number
                v-model="paymentForm.paymentAmount"
                :precision="2"
                :min="0"
                placeholder="回款金额"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回款日期" prop="paymentDate">
              <el-date-picker
                v-model="paymentForm.paymentDate"
                type="date"
                placeholder="选择回款日期"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="回款方式" prop="paymentMethod">
              <el-select v-model="paymentForm.paymentMethod" placeholder="请选择回款方式" style="width: 100%">
                <el-option label="银行转账" value="银行转账" />
                <el-option label="现金" value="现金" />
                <el-option label="支票" value="支票" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回款账户" prop="paymentAccount">
              <el-input v-model="paymentForm.paymentAccount" placeholder="请输入回款账户" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="银行信息">
          <el-input v-model="paymentForm.bankInfo" placeholder="请输入银行信息" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="paymentForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="paymentDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitPayment" :loading="paymentSubmitting">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getPaymentSummary, addInvoice, addPayment } from '@/api/contract/financial'

const props = defineProps({
  contractId: {
    type: Number,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// 汇总数据
const summaryData = ref({
  contractAmount: 0,
  totalInvoiceAmount: 0,
  totalPaymentAmount: 0,
  pendingPaymentAmount: 0
})

// 开票记录
const invoiceList = ref([])
// 回款记录
const paymentList = ref([])

// 对话框状态
const invoiceDialogVisible = ref(false)
const paymentDialogVisible = ref(false)
const invoiceSubmitting = ref(false)
const paymentSubmitting = ref(false)

// 表单引用
const invoiceFormRef = ref(null)
const paymentFormRef = ref(null)

// 开票表单
const invoiceForm = ref({
  contractId: null,
  invoiceNumber: '',
  invoiceAmount: null,
  invoiceDate: '',
  invoiceType: '',
  taxRate: 13,
  taxAmount: null,
  remark: ''
})

// 回款表单
const paymentForm = ref({
  contractId: null,
  paymentAmount: null,
  paymentDate: '',
  paymentMethod: '',
  paymentAccount: '',
  bankInfo: '',
  remark: ''
})

// 表单验证规则
const invoiceRules = {
  invoiceNumber: [{ required: true, message: '请输入发票号码', trigger: 'blur' }],
  invoiceAmount: [{ required: true, message: '请输入开票金额', trigger: 'blur' }],
  invoiceDate: [{ required: true, message: '请选择开票日期', trigger: 'change' }],
  invoiceType: [{ required: true, message: '请选择发票类型', trigger: 'change' }]
}

const paymentRules = {
  paymentAmount: [{ required: true, message: '请输入回款金额', trigger: 'blur' }],
  paymentDate: [{ required: true, message: '请选择回款日期', trigger: 'change' }],
  paymentMethod: [{ required: true, message: '请选择回款方式', trigger: 'change' }]
}

// 加载数据
const loadData = async () => {
  if (!props.contractId) return
  
  try {
    const response = await getPaymentSummary(props.contractId)
    if (response.code === 200) {
      const data = response.data
      summaryData.value = {
        contractAmount: data.contractAmount || 0,
        totalInvoiceAmount: data.totalInvoiceAmount || 0,
        totalPaymentAmount: data.totalPaymentAmount || 0,
        pendingPaymentAmount: data.pendingPaymentAmount || 0
      }
      invoiceList.value = data.invoices || []
      paymentList.value = data.payments || []
    }
  } catch (error) {
    console.error('加载回款数据失败:', error)
  }
}

// 显示新增开票对话框
const showAddInvoiceDialog = () => {
  invoiceForm.value = {
    contractId: props.contractId,
    invoiceNumber: '',
    invoiceAmount: null,
    invoiceDate: '',
    invoiceType: '',
    taxRate: 13,
    taxAmount: null,
    remark: ''
  }
  invoiceDialogVisible.value = true
}

// 显示新增回款对话框
const showAddPaymentDialog = () => {
  paymentForm.value = {
    contractId: props.contractId,
    paymentAmount: null,
    paymentDate: '',
    paymentMethod: '',
    paymentAccount: '',
    bankInfo: '',
    remark: ''
  }
  paymentDialogVisible.value = true
}

// 提交开票记录
const submitInvoice = async () => {
  try {
    await invoiceFormRef.value.validate()
    invoiceSubmitting.value = true
    
    const response = await addInvoice(invoiceForm.value)
    if (response.code === 200) {
      ElMessage.success('开票记录添加成功')
      invoiceDialogVisible.value = false
      await loadData()
    } else {
      ElMessage.error(response.msg || '添加失败')
    }
  } catch (error) {
    console.error('添加开票记录失败:', error)
    ElMessage.error('添加失败')
  } finally {
    invoiceSubmitting.value = false
  }
}

// 提交回款记录
const submitPayment = async () => {
  try {
    await paymentFormRef.value.validate()
    paymentSubmitting.value = true
    
    const response = await addPayment(paymentForm.value)
    if (response.code === 200) {
      ElMessage.success('回款记录添加成功')
      paymentDialogVisible.value = false
      await loadData()
    } else {
      ElMessage.error(response.msg || '添加失败')
    }
  } catch (error) {
    console.error('添加回款记录失败:', error)
    ElMessage.error('添加失败')
  } finally {
    paymentSubmitting.value = false
  }
}

// 删除开票记录
const deleteInvoice = async (invoice) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除发票号码为"${invoice.invoiceNumber}"的开票记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // TODO: 调用删除接口
    ElMessage.success('删除成功')
    await loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 删除回款记录
const deletePayment = async (payment) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除金额为"${payment.paymentAmount}"的回款记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // TODO: 调用删除接口
    ElMessage.success('删除成功')
    await loadData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 监听合同ID变化
watch(() => props.contractId, (newId) => {
  if (newId) {
    loadData()
  }
}, { immediate: true })

onMounted(() => {
  if (props.contractId) {
    loadData()
  }
})
</script>

<style scoped>
.contract-payment-management {
  padding: 20px;
}

.summary-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mt-4 {
  margin-top: 16px;
}
</style>
