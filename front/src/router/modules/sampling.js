import Layout from '@/layout'

export default {
  path: '/sampling',
  component: Layout,
  redirect: '/sampling/task',
  name: 'Sampling',
  meta: {
    title: '采样管理',
    icon: 'sampling'
  },
  children: [
    {
      path: 'task',
      component: () => import('@/views/sampling/task/index'),
      name: 'SamplingTask',
      meta: {
        title: '采样任务',
        icon: 'list'
      }
    },

    {
      path: 'cycle-item',
      component: () => import('@/views/sampling/cycleItem/index'),
      name: 'DetectionCycleItem',
      meta: {
        title: '检测周期条目',
        icon: 'time'
      }
    },
    {
      path: 'task-create',
      component: () => import('@/views/sampling/taskcreate/index'),
      name: 'SamplingTaskCreate',
      meta: {
        title: '采样任务创建',
        icon: 'assignment'
      }
    },
    {
      path: 'task-group',
      component: () => import('@/views/sampling/task-group/index'),
      name: 'SamplingTaskGroup',
      meta: {
        title: '分组管理',
        icon: 'group'
      }
    },
    {
      path: 'sample-box',
      component: () => import('@/views/sampling/sampleBox/index'),
      name: 'SampleBox',
      meta: {
        title: '样品箱管理',
        icon: 'box'
      }
    },
    {
      path: 'execution',
      component: () => import('@/views/sampling/execution/index'),
      name: 'SamplingExecution',
      meta: {
        title: '采样执行',
        icon: 'execute'
      }
    }
  ]
}