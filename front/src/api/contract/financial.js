import request from '@/utils/request'

// 获取合同财务汇总
export function getFinancialSummary(contractId) {
  return request({
    url: `/contract/financial/summary/${contractId}`,
    method: 'get'
  })
}

// 获取合同回款汇总
export function getPaymentSummary(contractId) {
  return request({
    url: `/contract/financial/payment/summary/${contractId}`,
    method: 'get'
  })
}

// 获取合同成本汇总
export function getCostSummary(contractId) {
  return request({
    url: `/contract/financial/cost/summary/${contractId}`,
    method: 'get'
  })
}

// 新增开票记录
export function addInvoice(data) {
  return request({
    url: '/contract/financial/invoice',
    method: 'post',
    data: data
  })
}

// 新增回款记录
export function addPayment(data) {
  return request({
    url: '/contract/financial/payment',
    method: 'post',
    data: data
  })
}

// 新增收票记录
export function addReceipt(data) {
  return request({
    url: '/contract/financial/receipt',
    method: 'post',
    data: data
  })
}

// 新增付款记录
export function addCostPayment(data) {
  return request({
    url: '/contract/financial/cost-payment',
    method: 'post',
    data: data
  })
}
