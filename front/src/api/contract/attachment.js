import request from '@/utils/request'

// 获取合同附件列表
export function getAttachmentList(query) {
  return request({
    url: '/contract/attachment/list',
    method: 'get',
    params: query
  })
}

// 删除合同附件
export function deleteAttachment(attachmentId) {
  return request({
    url: `/contract/attachment/${attachmentId}`,
    method: 'delete'
  })
}

// 下载合同附件
export function downloadAttachment(attachmentId) {
  return request({
    url: `/contract/attachment/download/${attachmentId}`,
    method: 'get',
    responseType: 'blob'
  })
}
