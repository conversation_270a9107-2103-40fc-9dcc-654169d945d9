import request from '@/utils/request'

// 查询样品箱列表
export function listSampleBox(query) {
  return request({
    url: '/sampling/sample-box/list',
    method: 'get',
    params: query
  })
}

// 查询样品箱详细
export function getSampleBox(boxId) {
  return request({
    url: '/sampling/sample-box/' + boxId,
    method: 'get'
  })
}

// 新增样品箱
export function addSampleBox(data) {
  return request({
    url: '/sampling/sample-box/create',
    method: 'post',
    data: data
  })
}

// 修改样品箱
export function updateSampleBox(boxId, data) {
  return request({
    url: '/sampling/sample-box/' + boxId,
    method: 'put',
    data: data
  })
}

// 删除样品箱
export function delSampleBox(boxId) {
  return request({
    url: '/sampling/sample-box/' + boxId,
    method: 'delete'
  })
}

// 封箱操作
export function sealSampleBox(boxId) {
  return request({
    url: '/sampling/sample-box/' + boxId + '/seal',
    method: 'put'
  })
}

// 根据任务ID获取样品箱列表
export function getSampleBoxesByTask(taskId) {
  return request({
    url: '/sampling/sample-box/task/' + taskId,
    method: 'get'
  })
}

// 获取任务的样品箱统计信息
export function getSampleBoxStatisticsByTask(taskId) {
  return request({
    url: '/sampling/sample-box/statistics/task/' + taskId,
    method: 'get'
  })
}

// 获取全部样品箱统计信息
export function getAllSampleBoxStatistics() {
  return request({
    url: '/sampling/sample-box/statistics/all',
    method: 'get'
  })
}
