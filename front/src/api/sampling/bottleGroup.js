import request from '@/utils/request'

// 获取任务的可用样品箱列表（新建状态）
export function getAvailableSampleBoxesByTask(taskId) {
  return request({
    url: `/sampling/bottle-groups/task/${taskId}/available-sample-boxes`,
    method: 'get'
  })
}

// 关联瓶组和样品箱
export function linkBottleGroupToSampleBox(bottleGroupId, sampleBoxId) {
  return request({
    url: `/sampling/bottle-groups/${bottleGroupId}/link-sample-box/${sampleBoxId}`,
    method: 'post'
  })
}