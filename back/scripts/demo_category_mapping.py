#!/usr/bin/env python3
"""
类别编码映射演示脚本
展示新的类别编码映射关系和样品编号生成示例
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.category_identifier_util import CategoryIdentifierUtil


def demo_category_mapping():
    """演示类别编码映射"""
    
    print("=" * 80)
    print("LIMS系统 - 类别编码映射演示")
    print("=" * 80)
    
    # 定义测试用的任务编号
    task_code = "25010001"
    
    # 按类别分组展示
    categories = {
        "气体类 (Q)": [
            "公共场所", "化学污染物", "物理因素",
            "气", "室内空气", "无组织废气", "有组织废气",
            "环境空气（小时值/一次值）", "环境空气（日均值）",
            "油气回收"
        ],
        "固体类 (G)": [
            "固", "固废（全量）", "固废（浸出）", "易燃性", "腐蚀性",
            "土", "土壤", "水系沉积物",
            "水处理剂", "活性炭（木质）", "活性炭（煤质）", "聚氯化铝",
            "水泥胶砂",
            "污泥", "医疗污泥", "城镇污泥",
            "海洋沉积物"
        ],
        "水体类 (S)": [
            "水", "地下水", "地表水", "大气降水", "废水", "生活饮用水",
            "海洋", "海水"
        ],
        "声学类 (Z)": [
            "声", "24h噪声", "噪声（夜）", "噪声（昼）",
            "振动", "震动"
        ],
        "生态类 (B)": [
            "生态", "水生生态调查", "生物监测（定性）", "生物监测（定量）"
        ],
        "其他类 (/)": [
            "能源", "辐射", "放射性物质", "电磁辐射", "电离辐射"
        ]
    }
    
    sequence = 1
    
    for category_type, classifications in categories.items():
        print(f"\n{category_type}")
        print("-" * 60)
        
        for classification in classifications:
            identifier = CategoryIdentifierUtil.get_identifier(classification)
            sample_number = f"{task_code}{identifier}{sequence:03d}"
            
            print(f"  {classification:<25} → {identifier:<3} → {sample_number}")
            sequence += 1
    
    print("\n" + "=" * 80)
    print("特殊情况演示")
    print("=" * 80)
    
    # 测试未知分类
    unknown_cases = ["未知分类", "不存在的分类", "", None]
    
    for case in unknown_cases:
        identifier = CategoryIdentifierUtil.get_identifier(case)
        sample_number = f"{task_code}{identifier}{sequence:03d}"
        case_display = repr(case) if case is not None else "None"
        
        print(f"  {case_display:<25} → {identifier:<3} → {sample_number}")
        sequence += 1
    
    print("\n" + "=" * 80)
    print("向后兼容性演示")
    print("=" * 80)
    
    # 测试向后兼容的映射
    compatibility_cases = [
        ("震动", "Z", "保持向后兼容，同时支持'振动'"),
        ("海洋", "S", "保持向后兼容"),
    ]
    
    for classification, expected_identifier, note in compatibility_cases:
        identifier = CategoryIdentifierUtil.get_identifier(classification)
        sample_number = f"{task_code}{identifier}{sequence:03d}"
        status = "✅" if identifier == expected_identifier else "❌"
        
        print(f"  {classification:<15} → {identifier:<3} → {sample_number} {status} {note}")
        sequence += 1
    
    print("\n" + "=" * 80)
    print("映射统计")
    print("=" * 80)
    
    all_mappings = CategoryIdentifierUtil.get_all_identifiers()
    
    # 统计各编码的使用情况
    identifier_counts = {}
    for classification, identifier in all_mappings.items():
        identifier_counts[identifier] = identifier_counts.get(identifier, 0) + 1
    
    print(f"总映射数量: {len(all_mappings)}")
    print("\n编码使用统计:")
    for identifier, count in sorted(identifier_counts.items()):
        print(f"  {identifier}: {count} 个分类")
    
    print("\n" + "=" * 80)
    print("质控样品编号示例")
    print("=" * 80)
    
    # 展示质控样品编号
    from utils.quality_control_identifier_util import QualityControlIdentifierUtil
    
    base_sample = f"{task_code}S001"  # 基础水样品
    qc_mappings = QualityControlIdentifierUtil.get_all_identifiers()
    
    print(f"基础样品编号: {base_sample}")
    print("\n质控样品编号:")
    
    qc_names = {
        "parallel_sample": "现场平行样",
        "lab_parallel_sample": "实验室平行样", 
        "matrix_spike_sample": "基体加标样",
        "full_blank_sample": "全程空白样",
        "transport_blank_sample": "运输空白样",
        "equipment_blank_sample": "设备清洗空白样"
    }
    
    for qc_type, qc_identifier in qc_mappings.items():
        qc_sample_number = f"{base_sample}{qc_identifier}"
        qc_name = qc_names.get(qc_type, qc_type)
        print(f"  {qc_name:<15} → {qc_identifier:<5} → {qc_sample_number}")
    
    print("\n" + "=" * 80)
    print("演示完成")
    print("=" * 80)


if __name__ == "__main__":
    demo_category_mapping()
