"""
样品箱控制器
"""

from typing import List, Optional, Annotated
from fastapi import APIRouter, Depends, HTTPException, Body, Query, Path
from pydantic import BeforeValidator
from sqlalchemy.ext.asyncio import AsyncSession

# Custom validator that converts string to int with error handling
def validate_int(v):
    if isinstance(v, int):
        return v
    elif isinstance(v, str):
        try:
            return int(v)
        except ValueError:
            raise ValueError(f"'{v}' is not a valid integer")
    else:
        raise ValueError(f"'{v}' is not a valid integer")

# Create a validated integer type with custom validation
ValidatedInt = Annotated[int, BeforeValidator(validate_int)]

from config.get_db import get_db
from module_admin.service.login_service import LoginService
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_sampling.service.sample_box_service import SampleBoxService
from module_sampling.dto.sample_box_dto import (
    SampleBoxCreateDTO, SampleBoxUpdateDTO, SampleBoxDTO,
    SampleBoxQueryDTO, SampleBoxStatisticsDTO, SampleBoxSealDTO
)
from utils.response_util import ResponseUtil
from utils.log_util import logger

router = APIRouter(prefix="/sampling/sample-box", tags=["样品箱管理"])


@router.post("/create", response_model=CrudResponseModel, summary="创建样品箱")
async def create_sample_box(
    create_dto: SampleBoxCreateDTO = Body(..., description="样品箱创建信息"),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    创建样品箱
    
    Args:
        create_dto: 样品箱创建信息
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        创建结果
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 开始创建样品箱")

        service = SampleBoxService(db)
        result = await service.create_sample_box(create_dto, current_user.user.user_id)

        logger.info(f"样品箱创建成功，ID: {result.id}")
        return ResponseUtil.success(data=result, msg="样品箱创建成功")

    except Exception as e:
        logger.error(f"创建样品箱失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list", response_model=CrudResponseModel, summary="分页查询样品箱列表")
async def list_sample_boxes(
    sampling_task_id: Optional[int] = Query(None, description="采样任务ID"),
    box_code: Optional[str] = Query(None, description="样品箱编号"),
    status: Optional[int] = Query(None, description="状态"),
    is_sealed: Optional[bool] = Query(None, description="是否已封箱"),
    page_num: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    分页查询样品箱列表

    Args:
        sampling_task_id: 采样任务ID
        box_code: 样品箱编号
        status: 状态
        is_sealed: 是否已封箱
        page_num: 页码
        page_size: 每页数量
        current_user: 当前用户
        db: 数据库会话

    Returns:
        样品箱列表
    """
    try:
        service = SampleBoxService(db)

        # 构建查询DTO
        query_dto = SampleBoxQueryDTO(
            sampling_task_id=sampling_task_id,
            box_code=box_code,
            status=status,
            is_sealed=is_sealed
        )

        result = await service.get_sample_boxes_with_pagination(
            query_dto, page_num, page_size
        )

        return ResponseUtil.success(data=result, msg="获取样品箱列表成功")

    except Exception as e:
        logger.error(f"获取样品箱列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{box_id}", response_model=CrudResponseModel, summary="获取样品箱详情")
async def get_sample_box(
    box_id: ValidatedInt = Path(..., description="样品箱ID", ge=1),  # ge=1 ensures value is greater than or equal to 1
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取样品箱详情
    
    Args:
        box_id: 样品箱ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        样品箱详情
    """
    try:
        service = SampleBoxService(db)
        result = await service.get_sample_box_by_id(box_id)

        if result:
            return ResponseUtil.success(data=result, msg="获取样品箱详情成功")
        else:
            return ResponseUtil.error(msg="样品箱不存在")

    except Exception as e:
        logger.error(f"获取样品箱详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{box_id}", response_model=CrudResponseModel, summary="更新样品箱")
async def update_sample_box(
    box_id: ValidatedInt = Path(..., description="样品箱ID", ge=1),
    update_dto: SampleBoxUpdateDTO = Body(..., description="样品箱更新信息"),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    更新样品箱
    
    Args:
        box_id: 样品箱ID
        update_dto: 样品箱更新信息
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        更新结果
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 开始更新样品箱 {box_id}")

        service = SampleBoxService(db)
        result = await service.update_sample_box(box_id, update_dto, current_user.user.user_id)

        logger.info(f"样品箱更新成功")
        return ResponseUtil.success(data=result, msg="样品箱更新成功")

    except Exception as e:
        logger.error(f"更新样品箱失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{box_id}/seal", response_model=CrudResponseModel, summary="封箱操作")
async def seal_sample_box(
    box_id: ValidatedInt = Path(..., description="样品箱ID", ge=1),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    封箱操作
    
    Args:
        box_id: 样品箱ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        封箱结果
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 开始封箱操作 {box_id}")

        service = SampleBoxService(db)
        result = await service.seal_sample_box(box_id, current_user.user.user_id)

        logger.info(f"封箱操作成功")
        return ResponseUtil.success(data=result, msg="封箱操作成功")

    except Exception as e:
        logger.error(f"封箱操作失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{box_id}", response_model=CrudResponseModel, summary="删除样品箱")
async def delete_sample_box(
    box_id: ValidatedInt = Path(..., description="样品箱ID", ge=1),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    删除样品箱
    
    Args:
        box_id: 样品箱ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        删除结果
    """
    try:
        logger.info(f"用户 {current_user.user.user_id} 开始删除样品箱 {box_id}")

        service = SampleBoxService(db)
        result = await service.delete_sample_box(box_id)

        if result:
            logger.info(f"样品箱删除成功")
            return ResponseUtil.success(msg="样品箱删除成功")
        else:
            return ResponseUtil.error(msg="样品箱删除失败")

    except Exception as e:
        logger.error(f"删除样品箱失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/task/{task_id}", response_model=CrudResponseModel, summary="获取任务的样品箱列表")
async def get_sample_boxes_by_task(
    task_id: ValidatedInt = Path(..., description="采样任务ID", ge=1),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取任务的样品箱列表
    
    Args:
        task_id: 采样任务ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        样品箱列表
    """
    try:
        service = SampleBoxService(db)
        result = await service.get_sample_boxes_by_task(task_id)

        return ResponseUtil.success(data=result, msg="获取样品箱列表成功")

    except Exception as e:
        logger.error(f"获取样品箱列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics/task/{task_id}", response_model=CrudResponseModel, summary="获取任务的样品箱统计信息")
async def get_sample_box_statistics_by_task(
    task_id: ValidatedInt = Path(..., description="采样任务ID", ge=1),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取任务的样品箱统计信息
    
    Args:
        task_id: 采样任务ID
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        统计信息
    """
    try:
        service = SampleBoxService(db)
        result = await service.get_sample_box_statistics(task_id)

        return ResponseUtil.success(data=result, msg="获取统计信息成功")

    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/statistics/all", response_model=CrudResponseModel, summary="获取全部样品箱统计信息")
async def get_all_sample_box_statistics(
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    获取全部样品箱统计信息

    Args:
        current_user: 当前用户
        db: 数据库会话

    Returns:
        统计信息
    """
    try:
        service = SampleBoxService(db)
        result = await service.get_sample_box_statistics()

        return ResponseUtil.success(data=result, msg="获取统计信息成功")

    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list", response_model=CrudResponseModel, summary="分页查询样品箱列表")
async def list_sample_boxes(
    sampling_task_id: Optional[int] = Query(None, description="采样任务ID"),
    box_code: Optional[str] = Query(None, description="样品箱编号"),
    status: Optional[int] = Query(None, description="状态"),
    is_sealed: Optional[bool] = Query(None, description="是否已封箱"),
    page_num: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
    db: AsyncSession = Depends(get_db)
) -> dict:
    """
    分页查询样品箱列表

    Args:
        sampling_task_id: 采样任务ID
        box_code: 样品箱编号
        status: 状态
        is_sealed: 是否已封箱
        page_num: 页码
        page_size: 每页数量
        current_user: 当前用户
        db: 数据库会话

    Returns:
        样品箱列表
    """
    try:
        service = SampleBoxService(db)

        # 构建查询DTO
        query_dto = SampleBoxQueryDTO(
            sampling_task_id=sampling_task_id,
            box_code=box_code,
            status=status,
            is_sealed=is_sealed
        )

        result = await service.get_sample_boxes_with_pagination(
            query_dto, page_num, page_size
        )

        return ResponseUtil.success(data=result, msg="获取样品箱列表成功")

    except Exception as e:
        logger.error(f"获取样品箱列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
