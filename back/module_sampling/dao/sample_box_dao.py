"""
样品箱数据访问层
"""

from typing import List, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, selectinload
from sqlalchemy import and_, or_, desc, asc, select, func, delete, update
from datetime import datetime, date

from module_sampling.entity.do.sample_box_do import SampleBox
from module_sampling.entity.do.sample_box_sample_do import SampleBoxSample
from module_sampling.entity.do.sample_box_sequence_do import SampleBoxSequence
from module_sampling.entity.do.sample_record_do import <PERSON>pleRecord
from module_sampling.entity.do.sampling_task_do import SamplingTask
from module_admin.entity.do.user_do import SysUser


class SampleBoxDAO:
    """样品箱数据访问层"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_sample_box(self, sample_box: SampleBox) -> SampleBox:
        """创建样品箱"""
        self.db.add(sample_box)
        await self.db.flush()
        await self.db.refresh(sample_box)
        return sample_box
    
    async def get_sample_box_by_id(self, box_id: int) -> Optional[SampleBox]:
        """根据ID获取样品箱"""
        stmt = select(SampleBox).where(SampleBox.id == box_id)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_sample_box_by_code(self, box_code: str) -> Optional[SampleBox]:
        """根据编号获取样品箱"""
        stmt = select(SampleBox).where(SampleBox.box_code == box_code)
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_sample_boxes_by_task(self, task_id: int) -> List[SampleBox]:
        """获取任务的样品箱列表"""
        stmt = select(SampleBox).where(SampleBox.sampling_task_id == task_id).order_by(desc(SampleBox.create_time))
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def update_sample_box(self, sample_box: SampleBox) -> SampleBox:
        """更新样品箱"""
        await self.db.flush()
        await self.db.refresh(sample_box)
        return sample_box
    
    async def delete_sample_box(self, box_id: int) -> bool:
        """删除样品箱"""
        stmt = delete(SampleBox).where(SampleBox.id == box_id)
        result = await self.db.execute(stmt)
        return result.rowcount > 0
    
    async def get_sample_boxes_with_details(self, 
                                          task_id: Optional[int] = None,
                                          box_code: Optional[str] = None,
                                          status: Optional[int] = None,
                                          is_sealed: Optional[bool] = None,
                                          create_time_start: Optional[datetime] = None,
                                          create_time_end: Optional[datetime] = None,
                                          page_num: int = 1,
                                          page_size: int = 10) -> Tuple[List[SampleBox], int]:
        """分页查询样品箱（包含详细信息）"""
        # 构建查询条件
        conditions = []
        if task_id:
            conditions.append(SampleBox.sampling_task_id == task_id)
        if box_code:
            conditions.append(SampleBox.box_code.like(f'%{box_code}%'))
        if status is not None:
            conditions.append(SampleBox.status == status)
        if is_sealed is not None:
            conditions.append(SampleBox.is_sealed == is_sealed)
        if create_time_start:
            conditions.append(SampleBox.create_time >= create_time_start)
        if create_time_end:
            conditions.append(SampleBox.create_time <= create_time_end)
        
        # 查询总数
        count_stmt = select(func.count(SampleBox.id))
        if conditions:
            count_stmt = count_stmt.where(and_(*conditions))
        count_result = await self.db.execute(count_stmt)
        total = count_result.scalar() or 0
        
        # 分页查询（暂时不使用joinedload，避免关系映射问题）
        stmt = select(SampleBox)
        if conditions:
            stmt = stmt.where(and_(*conditions))
        
        stmt = stmt.order_by(desc(SampleBox.create_time))
        stmt = stmt.offset((page_num - 1) * page_size).limit(page_size)
        
        result = await self.db.execute(stmt)
        sample_boxes = result.scalars().all()
        
        return sample_boxes, total
    
    async def get_sample_box_count_by_task(self, task_id: int) -> int:
        """根据任务ID获取样品箱数量"""
        stmt = select(func.count(SampleBox.id)).where(SampleBox.sampling_task_id == task_id)
        result = await self.db.execute(stmt)
        count = result.scalar_one()
        return count

    async def get_sample_boxes_by_task_and_status(self, task_id: int, status: int) -> List[SampleBox]:
        """根据任务ID和状态获取样品箱列表"""
        stmt = select(SampleBox).where(
            SampleBox.sampling_task_id == task_id,
            SampleBox.status == status
        ).order_by(desc(SampleBox.create_time))
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_next_sequence_number(self, date_key: str) -> int:
        """获取下一个序号"""
        # 查找或创建序列记录
        stmt = select(SampleBoxSequence).where(SampleBoxSequence.date_key == date_key)
        result = await self.db.execute(stmt)
        sequence = result.scalar_one_or_none()
        
        if sequence is None:
            # 创建新的序列记录
            sequence = SampleBoxSequence(date_key=date_key, sequence_number=1)
            self.db.add(sequence)
            await self.db.flush()
            return 1
        else:
            # 更新序列号
            sequence.sequence_number += 1
            await self.db.flush()
            return sequence.sequence_number
    
    async def get_sample_box_statistics(self, task_id: Optional[int] = None) -> dict:
        """获取样品箱统计信息"""
        conditions = []
        if task_id:
            conditions.append(SampleBox.sampling_task_id == task_id)
        
        # 统计各状态数量
        stmt = select(
            func.count(SampleBox.id).label('total_count'),
            func.sum(func.case((SampleBox.status == 0, 1), else_=0)).label('new_count'),
            func.sum(func.case((SampleBox.status == 1, 1), else_=0)).label('sealed_count'),
            func.sum(func.case((SampleBox.status == 2, 1), else_=0)).label('sent_count'),
            func.sum(SampleBox.sample_count).label('total_samples')
        )
        
        if conditions:
            stmt = stmt.where(and_(*conditions))
        
        result = await self.db.execute(stmt)
        stats = result.first()
        
        return {
            'total_count': stats.total_count or 0,
            'new_count': stats.new_count or 0,
            'sealed_count': stats.sealed_count or 0,
            'sent_count': stats.sent_count or 0,
            'total_samples': stats.total_samples or 0
        }
