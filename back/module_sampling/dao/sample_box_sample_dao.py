"""
样品箱样品关联数据访问层
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload
from sqlalchemy import and_, select, delete, func
from datetime import datetime

from module_sampling.entity.do.sample_box_sample_do import SampleBoxSample
from module_sampling.entity.do.sample_record_do import SampleRecord


class SampleBoxSampleDAO:
    """样品箱样品关联数据访问层"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def add_sample_to_box(self, box_id: int, sample_record_id: int, create_by: int) -> SampleBoxSample:
        """将样品添加到样品箱"""
        relation = SampleBoxSample(
            sample_box_id=box_id,
            sample_record_id=sample_record_id,
            create_by=create_by
        )
        self.db.add(relation)
        await self.db.flush()
        await self.db.refresh(relation)
        return relation
    
    async def remove_sample_from_box(self, box_id: int, sample_record_id: int) -> bool:
        """从样品箱中移除样品"""
        stmt = delete(SampleBoxSample).where(
            and_(
                SampleBoxSample.sample_box_id == box_id,
                SampleBoxSample.sample_record_id == sample_record_id
            )
        )
        result = await self.db.execute(stmt)
        return result.rowcount > 0
    
    async def clear_box_samples(self, box_id: int) -> bool:
        """清空样品箱中的所有样品"""
        stmt = delete(SampleBoxSample).where(SampleBoxSample.sample_box_id == box_id)
        result = await self.db.execute(stmt)
        return result.rowcount > 0
    
    async def get_box_samples(self, box_id: int) -> List[SampleRecord]:
        """获取样品箱中的样品列表"""
        stmt = select(SampleRecord).join(
            SampleBoxSample, SampleRecord.id == SampleBoxSample.sample_record_id
        ).where(SampleBoxSample.sample_box_id == box_id).order_by(SampleRecord.sample_number)
        
        result = await self.db.execute(stmt)
        return result.scalars().all()
    
    async def get_sample_boxes_by_sample(self, sample_record_id: int) -> List[int]:
        """获取样品所在的样品箱ID列表"""
        stmt = select(SampleBoxSample.sample_box_id).where(
            SampleBoxSample.sample_record_id == sample_record_id
        )
        result = await self.db.execute(stmt)
        return [row[0] for row in result.fetchall()]
    
    async def is_sample_in_box(self, box_id: int, sample_record_id: int) -> bool:
        """检查样品是否在指定样品箱中"""
        stmt = select(SampleBoxSample).where(
            and_(
                SampleBoxSample.sample_box_id == box_id,
                SampleBoxSample.sample_record_id == sample_record_id
            )
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none() is not None
    
    async def get_box_sample_count(self, box_id: int) -> int:
        """获取样品箱中的样品数量"""
        stmt = select(func.count(SampleBoxSample.id)).where(
            SampleBoxSample.sample_box_id == box_id
        )
        result = await self.db.execute(stmt)
        return result.scalar() or 0
    
    async def batch_add_samples_to_box(self, box_id: int, sample_record_ids: List[int], create_by: int) -> List[SampleBoxSample]:
        """批量将样品添加到样品箱"""
        relations = []
        for sample_record_id in sample_record_ids:
            # 检查是否已存在关联
            if not await self.is_sample_in_box(box_id, sample_record_id):
                relation = SampleBoxSample(
                    sample_box_id=box_id,
                    sample_record_id=sample_record_id,
                    create_by=create_by
                )
                self.db.add(relation)
                relations.append(relation)
        
        if relations:
            await self.db.flush()
            for relation in relations:
                await self.db.refresh(relation)
        
        return relations
    
    async def batch_remove_samples_from_box(self, box_id: int, sample_record_ids: List[int]) -> int:
        """批量从样品箱中移除样品"""
        stmt = delete(SampleBoxSample).where(
            and_(
                SampleBoxSample.sample_box_id == box_id,
                SampleBoxSample.sample_record_id.in_(sample_record_ids)
            )
        )
        result = await self.db.execute(stmt)
        return result.rowcount
