"""
样品箱-瓶组关联数据访问层
"""

from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from module_sampling.entity.do.sample_box_bottle_group_do import SampleBoxBottleGroup


class SampleBoxBottleGroupDAO:
    """样品箱-瓶组关联数据访问层"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_relation(self, sample_box_id: int, bottle_group_id: int, create_by: int) -> SampleBoxBottleGroup:
        """创建样品箱-瓶组关联"""
        relation = SampleBoxBottleGroup(
            sample_box_id=sample_box_id,
            bottle_group_id=bottle_group_id,
            create_by=create_by,
            update_by=create_by
        )
        self.db.add(relation)
        await self.db.flush()
        return relation

    async def delete_relation(self, sample_box_id: int, bottle_group_id: int) -> bool:
        """删除样品箱-瓶组关联"""
        stmt = delete(SampleBoxBottleGroup).where(
            SampleBoxBottleGroup.sample_box_id == sample_box_id,
            SampleBoxBottleGroup.bottle_group_id == bottle_group_id
        )
        result = await self.db.execute(stmt)
        await self.db.flush()
        return result.rowcount > 0

    async def get_bottle_groups_by_sample_box_id(self, sample_box_id: int) -> List[SampleBoxBottleGroup]:
        """根据样品箱ID获取关联的瓶组"""
        stmt = select(SampleBoxBottleGroup).where(
            SampleBoxBottleGroup.sample_box_id == sample_box_id
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def get_sample_boxes_by_bottle_group_id(self, bottle_group_id: int) -> List[SampleBoxBottleGroup]:
        """根据瓶组ID获取关联的样品箱"""
        stmt = select(SampleBoxBottleGroup).where(
            SampleBoxBottleGroup.bottle_group_id == bottle_group_id
        )
        result = await self.db.execute(stmt)
        return result.scalars().all()

    async def exists_relation(self, sample_box_id: int, bottle_group_id: int) -> bool:
        """检查样品箱-瓶组关联是否存在"""
        stmt = select(SampleBoxBottleGroup).where(
            SampleBoxBottleGroup.sample_box_id == sample_box_id,
            SampleBoxBottleGroup.bottle_group_id == bottle_group_id
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none() is not None