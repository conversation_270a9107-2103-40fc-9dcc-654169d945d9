"""
样品箱样品关联表数据模型
"""

from sqlalchemy import Column, BigInteger, DateTime, ForeignKey, Index, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base


class SampleBoxSample(Base):
    """
    样品箱样品关联表
    """
    __tablename__ = 'sample_box_sample'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sample_box_id = Column(BigInteger, ForeignKey('sample_box.id'), nullable=False, comment='样品箱ID')
    sample_record_id = Column(BigInteger, ForeignKey('sample_record.id'), nullable=False, comment='样品记录ID')
    
    # 审计字段
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    
    # 关系定义（暂时注释，避免循环导入）
    # sample_box = relationship("SampleBox", back_populates="sample_relations")
    # sample_record = relationship("SampleRecord", back_populates="box_relations")
    # creator = relationship("SysUser", foreign_keys=[create_by])
    
    # 索引和约束
    __table_args__ = (
        Index('idx_sample_box_sample_box_id', 'sample_box_id'),
        Index('idx_sample_box_sample_record_id', 'sample_record_id'),
        UniqueConstraint('sample_box_id', 'sample_record_id', name='uk_box_sample'),
        {'comment': '样品箱样品关联表'}
    )
    
    def __repr__(self):
        return f"<SampleBoxSample(id={self.id}, sample_box_id={self.sample_box_id}, sample_record_id={self.sample_record_id})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'sample_box_id': self.sample_box_id,
            'sample_record_id': self.sample_record_id,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'create_by': self.create_by
        }
