"""
样品箱-瓶组关联表数据模型
"""

from sqlalchemy import Column, BigInteger, Integer, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base


class SampleBoxBottleGroup(Base):
    """
    样品箱-瓶组关联表
    """
    __tablename__ = 'sample_box_bottle_group'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sample_box_id = Column(BigInteger, ForeignKey('sample_box.id'), nullable=False, comment='样品箱ID')
    bottle_group_id = Column(BigInteger, ForeignKey('sampling_bottle_group.id'), nullable=False, comment='瓶组ID')
    
    # 审计字段
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='更新人')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系定义（暂时注释，避免循环导入）
    # sample_box = relationship("SampleBox", back_populates="bottle_group_relations")
    # bottle_group = relationship("SamplingBottleGroup", back_populates="sample_box_relations")
    
    def __repr__(self):
        return f"<SampleBoxBottleGroup(sample_box_id={self.sample_box_id}, bottle_group_id={self.bottle_group_id})>"
