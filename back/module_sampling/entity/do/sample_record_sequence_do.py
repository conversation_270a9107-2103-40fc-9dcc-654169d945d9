#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
样品记录序号实体类
"""

from sqlalchemy import Column, BigInteger, Integer, DateTime, ForeignKey, UniqueConstraint, func
from config.database import Base


class SampleRecordSequence(Base):
    """
    样品记录序号表
    用于管理每个任务的样品序号，确保任务级别的序号连续递增
    """
    __tablename__ = 'sample_record_sequence'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sampling_task_id = Column(BigInteger, ForeignKey('sampling_task.id'), nullable=False, comment='采样任务ID')
    
    # 业务字段
    current_sequence = Column(Integer, default=0, comment='当前序号')
    
    # 审计字段
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 约束
    __table_args__ = (
        UniqueConstraint('sampling_task_id', name='uk_task_sample_sequence'),
        {'comment': '样品记录序号表'}
    )
    
    def __repr__(self):
        return f"<SampleRecordSequence(id={self.id}, sampling_task_id={self.sampling_task_id}, current_sequence={self.current_sequence})>"
