"""
样品箱编号序列表数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Index, UniqueConstraint
from sqlalchemy.sql import func
from config.database import Base


class SampleBoxSequence(Base):
    """
    样品箱编号序列表
    用于生成唯一的样品箱编号
    """
    __tablename__ = 'sample_box_sequence'
    
    # 主键
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 业务字段
    date_key = Column(String(8), nullable=False, comment='日期键（YYYYMMDD格式）')
    sequence_number = Column(Integer, default=0, comment='当日序号')
    
    # 审计字段
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 索引和约束
    __table_args__ = (
        UniqueConstraint('date_key', name='uk_date_key'),
        Index('idx_sample_box_sequence_date', 'date_key'),
        {'comment': '样品箱编号序列表'}
    )
    
    def __repr__(self):
        return f"<SampleBoxSequence(id={self.id}, date_key='{self.date_key}', sequence_number={self.sequence_number})>"
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'date_key': self.date_key,
            'sequence_number': self.sequence_number,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
