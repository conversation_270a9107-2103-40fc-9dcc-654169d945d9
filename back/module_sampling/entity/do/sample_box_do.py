"""
样品箱表数据模型
"""

from sqlalchemy import Column, BigInteger, Integer, String, DateTime, ForeignKey, Index, Text, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base


class SampleBox(Base):
    """
    样品箱表
    """
    __tablename__ = 'sample_box'
    
    # 主键
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='主键ID')
    
    # 关联字段
    sampling_task_id = Column(BigInteger, ForeignKey('sampling_task.id'), nullable=False, comment='采样任务ID')
    
    # 业务字段
    box_code = Column(String(100), nullable=False, unique=True, comment='样品箱编号（格式：XP+YYYYMMDD+序号）')
    box_name = Column(String(200), comment='样品箱名称')
    description = Column(Text, comment='样品箱描述')
    sample_count = Column(Integer, default=0, comment='样品数量')
    
    # 状态字段
    status = Column(Integer, default=0, comment='状态：0-新建，1-已封箱，2-已发送')
    is_sealed = Column(Boolean, default=False, comment='是否已封箱')
    sealed_time = Column(DateTime, comment='封箱时间')
    sealed_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='封箱人')
    
    # 审计字段
    create_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='创建人')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_by = Column(BigInteger, ForeignKey('sys_user.user_id'), comment='更新人')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关系定义（暂时注释，避免循环导入）
    # sampling_task = relationship("SamplingTask", back_populates="sample_boxes")
    # creator = relationship("SysUser", foreign_keys=[create_by])
    # updater = relationship("SysUser", foreign_keys=[update_by])
    # sealer = relationship("SysUser", foreign_keys=[sealed_by])
    
    # 索引
    __table_args__ = (
        Index('idx_sample_box_task_id', 'sampling_task_id'),
        Index('idx_sample_box_code', 'box_code'),
        Index('idx_sample_box_status', 'status'),
        Index('idx_sample_box_create_time', 'create_time'),
        {'comment': '样品箱表'}
    )
    
    def __repr__(self):
        return f"<SampleBox(id={self.id}, box_code='{self.box_code}', sampling_task_id={self.sampling_task_id}, status={self.status})>"
    
    @property
    def status_label(self):
        """状态标签"""
        status_map = {
            0: '新建',
            1: '已封箱',
            2: '已发送'
        }
        return status_map.get(self.status, '未知状态')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'sampling_task_id': self.sampling_task_id,
            'box_code': self.box_code,
            'box_name': self.box_name,
            'description': self.description,
            'sample_count': self.sample_count,
            'status': self.status,
            'status_label': self.status_label,
            'is_sealed': self.is_sealed,
            'sealed_time': self.sealed_time.isoformat() if self.sealed_time else None,
            'sealed_by': self.sealed_by,
            'create_by': self.create_by,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_by': self.update_by,
            'update_time': self.update_time.isoformat() if self.update_time else None
        }
