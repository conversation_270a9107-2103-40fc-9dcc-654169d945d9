"""
样品箱服务层
"""

from typing import List, Optional, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import datetime, date

from module_sampling.entity.do.sample_box_do import SampleBox
from module_sampling.entity.do.sample_record_do import <PERSON><PERSON>Record
from module_sampling.entity.do.sampling_task_do import SamplingTask
from module_admin.entity.do.user_do import SysUser

from module_sampling.dao.sample_box_dao import <PERSON><PERSON><PERSON>oxDAO
from module_sampling.dao.sample_box_sample_dao import SampleBoxSampleDAO
from module_sampling.dto.sample_box_dto import (
    SampleBoxCreateDTO, SampleBoxUpdateDTO, SampleBoxDTO, SampleBoxQueryDTO,
    SampleBoxStatisticsDTO, SampleRecordSimpleDTO, SampleBoxSealDTO
)
from exceptions.exception import ServiceException
from utils.log_util import logger


class SampleBoxService:
    """样品箱服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.sample_box_dao = SampleBoxDAO(db)
        self.sample_box_sample_dao = SampleBoxSampleDAO(db)
    
    async def create_sample_box(self, create_dto: SampleBoxCreateDTO, create_by: int) -> SampleBoxDTO:
        """创建样品箱"""
        try:
            # 验证采样任务是否存在
            task_stmt = select(SamplingTask).where(SamplingTask.id == create_dto.sampling_task_id)
            task_result = await self.db.execute(task_stmt)
            task = task_result.scalar_one_or_none()
            if not task:
                raise ServiceException(message="采样任务不存在")
            
            # 生成样品箱编号：基于采样任务编号和递增序号
            # 获取该任务已有的样品箱数量，以确定下一个序号
            existing_boxes_count = await self.sample_box_dao.get_sample_box_count_by_task(create_dto.sampling_task_id)
            box_sequence_number = existing_boxes_count + 1
            task_code = task.task_code if task.task_code else f"TASK{create_dto.sampling_task_id}"
            box_code = f"BOX{task_code}{box_sequence_number:03d}"
            
            # 创建样品箱
            sample_box = SampleBox(
                sampling_task_id=create_dto.sampling_task_id,
                box_code=box_code,
                box_name=f"样品箱{box_sequence_number:03d}",  # 自动生成名称
                description=create_dto.description,
                create_by=create_by,
                update_by=create_by
            )
            
            sample_box = await self.sample_box_dao.create_sample_box(sample_box)
            
            await self.db.commit()
            
            return await self._convert_to_dto(sample_box)
            
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"创建样品箱失败: {str(e)}")
    
    async def get_sample_box_by_id(self, box_id: int) -> Optional[SampleBoxDTO]:
        """根据ID获取样品箱"""
        try:
            sample_box = await self.sample_box_dao.get_sample_box_by_id(box_id)
            if not sample_box:
                return None
            
            return await self._convert_to_dto(sample_box)
            
        except Exception as e:
            raise ServiceException(message=f"获取样品箱失败: {str(e)}")
    
    async def update_sample_box(self, box_id: int, update_dto: SampleBoxUpdateDTO, update_by: int) -> SampleBoxDTO:
        """更新样品箱"""
        try:
            sample_box = await self.sample_box_dao.get_sample_box_by_id(box_id)
            if not sample_box:
                raise ServiceException(message="样品箱不存在")
            
            # 检查是否已封箱
            if sample_box.is_sealed:
                raise ServiceException(message="已封箱的样品箱不能修改")
            
            # 更新基本信息
            if update_dto.description is not None:
                sample_box.description = update_dto.description
            
            sample_box.update_by = update_by
            sample_box = await self.sample_box_dao.update_sample_box(sample_box)
            
            await self.db.commit()
            
            return await self._convert_to_dto(sample_box)
            
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"更新样品箱失败: {str(e)}")
    
    async def seal_sample_box(self, box_id: int, seal_by: int) -> SampleBoxDTO:
        """封箱操作"""
        try:
            sample_box = await self.sample_box_dao.get_sample_box_by_id(box_id)
            if not sample_box:
                raise ServiceException(message="样品箱不存在")
            
            if sample_box.is_sealed:
                raise ServiceException(message="样品箱已经封箱")
            
            # 更新封箱状态
            sample_box.is_sealed = True
            sample_box.status = 1  # 已封箱
            sample_box.sealed_time = datetime.now()
            sample_box.sealed_by = seal_by
            sample_box.update_by = seal_by
            
            sample_box = await self.sample_box_dao.update_sample_box(sample_box)
            await self.db.commit()
            
            return await self._convert_to_dto(sample_box)
            
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"封箱失败: {str(e)}")
    
    async def delete_sample_box(self, box_id: int) -> bool:
        """删除样品箱"""
        try:
            sample_box = await self.sample_box_dao.get_sample_box_by_id(box_id)
            if not sample_box:
                raise ServiceException(message="样品箱不存在")
            
            if sample_box.is_sealed:
                raise ServiceException(message="已封箱的样品箱不能删除")
            
            # 删除样品箱
            result = await self.sample_box_dao.delete_sample_box(box_id)
            await self.db.commit()
            
            return result
            
        except Exception as e:
            await self.db.rollback()
            raise ServiceException(message=f"删除样品箱失败: {str(e)}")
    
    async def get_sample_boxes_by_task(self, task_id: int) -> List[SampleBoxDTO]:
        """获取任务的样品箱列表"""
        try:
            sample_boxes = await self.sample_box_dao.get_sample_boxes_by_task(task_id)
            
            result_dtos = []
            for sample_box in sample_boxes:
                dto = await self._convert_to_dto(sample_box)
                result_dtos.append(dto)
            
            return result_dtos
            
        except Exception as e:
            raise ServiceException(message=f"获取样品箱列表失败: {str(e)}")
    
    async def get_sample_box_statistics(self, task_id: Optional[int] = None) -> SampleBoxStatisticsDTO:
        """获取样品箱统计信息"""
        try:
            stats = await self.sample_box_dao.get_sample_box_statistics(task_id)
            return SampleBoxStatisticsDTO(**stats)

        except Exception as e:
            raise ServiceException(message=f"获取统计信息失败: {str(e)}")

    async def get_sample_boxes_with_pagination(self, query_dto: SampleBoxQueryDTO, page_num: int, page_size: int) -> dict:
        """分页查询样品箱"""
        try:
            sample_boxes, total = await self.sample_box_dao.get_sample_boxes_with_details(
                task_id=query_dto.sampling_task_id,
                box_code=query_dto.box_code,
                status=query_dto.status,
                is_sealed=query_dto.is_sealed,
                create_time_start=query_dto.create_time_start,
                create_time_end=query_dto.create_time_end,
                page_num=page_num,
                page_size=page_size
            )

            result_dtos = []
            for sample_box in sample_boxes:
                dto = await self._convert_to_dto(sample_box)
                result_dtos.append(dto)

            return {
                'rows': result_dtos,
                'total': total
            }

        except Exception as e:
            raise ServiceException(message=f"分页查询样品箱失败: {str(e)}")
    
    async def _convert_to_dto(self, sample_box: SampleBox) -> SampleBoxDTO:
        """转换为DTO"""
        # 获取关联信息
        task_stmt = select(SamplingTask).where(SamplingTask.id == sample_box.sampling_task_id)
        task_result = await self.db.execute(task_stmt)
        task = task_result.scalar_one_or_none()
        
        # 获取用户信息
        creator_name = None
        updater_name = None
        sealer_name = None
        
        if sample_box.create_by:
            creator_stmt = select(SysUser).where(SysUser.user_id == sample_box.create_by)
            creator_result = await self.db.execute(creator_stmt)
            creator = creator_result.scalar_one_or_none()
            creator_name = creator.nick_name if creator else None
        
        if sample_box.update_by:
            updater_stmt = select(SysUser).where(SysUser.user_id == sample_box.update_by)
            updater_result = await self.db.execute(updater_stmt)
            updater = updater_result.scalar_one_or_none()
            updater_name = updater.nick_name if updater else None
        
        if sample_box.sealed_by:
            sealer_stmt = select(SysUser).where(SysUser.user_id == sample_box.sealed_by)
            sealer_result = await self.db.execute(sealer_stmt)
            sealer = sealer_result.scalar_one_or_none()
            sealer_name = sealer.nick_name if sealer else None
        
        return SampleBoxDTO(
            id=sample_box.id,
            sampling_task_id=sample_box.sampling_task_id,
            box_code=sample_box.box_code,
            description=sample_box.description,
            sample_count=sample_box.sample_count,
            status=sample_box.status,
            status_label=sample_box.status_label,
            is_sealed=sample_box.is_sealed,
            sealed_time=sample_box.sealed_time,
            sealed_by=sample_box.sealed_by,
            sealed_by_name=sealer_name,
            create_by=sample_box.create_by,
            create_by_name=creator_name,
            create_time=sample_box.create_time,
            update_by=sample_box.update_by,
            update_by_name=updater_name,
            update_time=sample_box.update_time,
            task_name=task.task_name if task else None,
            task_code=task.task_code if task else None
        )
