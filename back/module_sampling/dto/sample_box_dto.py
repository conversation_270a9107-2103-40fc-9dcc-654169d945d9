"""
样品箱数据传输对象
"""

from typing import Optional, List
from pydantic import BaseModel, Field
from pydantic.alias_generators import to_camel
from pydantic.config import ConfigDict
from datetime import datetime


class SampleBoxCreateDTO(BaseModel):
    """样品箱创建DTO"""
    sampling_task_id: int = Field(..., alias="samplingTaskId", description="采样任务ID")
    description: Optional[str] = Field(None, alias="description", description="样品箱描述")

    model_config = ConfigDict(populate_by_name=True)


class SampleBoxUpdateDTO(BaseModel):
    """样品箱更新DTO"""
    description: Optional[str] = Field(None, alias="description", description="样品箱描述")

    model_config = ConfigDict(populate_by_name=True)


class SampleBoxSealDTO(BaseModel):
    """样品箱封箱DTO"""
    pass  # 封箱操作不需要额外参数，只需要更新状态和时间


class SampleBoxQueryDTO(BaseModel):
    """样品箱查询DTO"""
    sampling_task_id: Optional[int] = Field(None, alias="samplingTaskId", description="采样任务ID")
    box_code: Optional[str] = Field(None, alias="boxCode", description="样品箱编号")
    status: Optional[int] = Field(None, alias="status", description="状态")
    is_sealed: Optional[bool] = Field(None, alias="isSealed", description="是否已封箱")
    create_time_start: Optional[datetime] = Field(None, alias="create_time_start", description="创建时间开始")
    create_time_end: Optional[datetime] = Field(None, alias="create_time_end", description="创建时间结束")

    model_config = ConfigDict(populate_by_name=True)


class SampleRecordSimpleDTO(BaseModel):
    """样品记录简单DTO（用于样品箱详情）"""
    id: int = Field(..., alias="id", description="样品记录ID")
    sample_number: str = Field(..., alias="sampleNumber", description="样品编号")
    sample_type: Optional[str] = Field(None, alias="sampleType", description="样品类型")
    sample_source: Optional[str] = Field(None, alias="sampleSource", description="样品来源")
    point_name: Optional[str] = Field(None, alias="pointName", description="点位名称")
    detection_category: Optional[str] = Field(None, alias="detectionCategory", description="检测类别")
    status: int = Field(..., alias="status", description="样品状态")
    status_label: str = Field(..., alias="statusLabel", description="状态标签")

    model_config = ConfigDict(populate_by_name=True)


class SampleBoxDTO(BaseModel):
    """样品箱DTO"""
    id: int = Field(..., alias="id", description="样品箱ID")
    sampling_task_id: int = Field(..., alias="samplingTaskId", description="采样任务ID")
    box_code: str = Field(..., alias="boxCode", description="样品箱编号")
    description: Optional[str] = Field(None, alias="description", description="样品箱描述")
    sample_count: int = Field(0, alias="sampleCount", description="样品数量")
    status: int = Field(..., alias="status", description="状态")
    status_label: str = Field(..., alias="statusLabel", description="状态标签")
    is_sealed: bool = Field(False, alias="isSealed", description="是否已封箱")
    sealed_time: Optional[datetime] = Field(None, alias="sealedTime", description="封箱时间")
    sealed_by: Optional[int] = Field(None, alias="sealedBy", description="封箱人ID")
    sealed_by_name: Optional[str] = Field(None, alias="sealedByName", description="封箱人姓名")
    create_by: Optional[int] = Field(None, alias="createBy", description="创建人ID")
    create_by_name: Optional[str] = Field(None, alias="createByName", description="创建人姓名")
    create_time: Optional[datetime] = Field(None, alias="createTime", description="创建时间")
    update_by: Optional[int] = Field(None, alias="updateBy", description="更新人ID")
    update_by_name: Optional[str] = Field(None, alias="updateByName", description="更新人姓名")
    update_time: Optional[datetime] = Field(None, alias="updateTime", description="更新时间")
    
    # 关联数据
    task_name: Optional[str] = Field(None, alias="taskName", description="任务名称")
    task_code: Optional[str] = Field(None, alias="taskCode", description="任务编号")
    samples: Optional[List[SampleRecordSimpleDTO]] = Field(default_factory=list, alias="samples", description="样品列表")

    model_config = ConfigDict(populate_by_name=True)


class SampleBoxStatisticsDTO(BaseModel):
    """样品箱统计DTO"""
    total_count: int = Field(0, alias="totalCount", description="总数量")
    new_count: int = Field(0, alias="newCount", description="新建数量")
    sealed_count: int = Field(0, alias="sealedCount", description="已封箱数量")
    sent_count: int = Field(0, alias="sentCount", description="已发送数量")
    total_samples: int = Field(0, alias="totalSamples", description="总样品数量")

    model_config = ConfigDict(populate_by_name=True)


class SampleBoxBatchCreateDTO(BaseModel):
    """样品箱批量创建DTO"""
    sampling_task_id: int = Field(..., alias="samplingTaskId", description="采样任务ID")
    box_configs: List[dict] = Field(..., alias="boxConfigs", description="样品箱配置列表")

    model_config = ConfigDict(
        populate_by_name=True,
        json_schema_extra={
            "example": {
                "samplingTaskId": 1,
                "boxConfigs": [
                    {
                        "boxName": "样品箱1",
                        "description": "第一批样品",
                        "sampleRecordIds": [1, 2, 3]
                    },
                    {
                        "boxName": "样品箱2", 
                        "description": "第二批样品",
                        "sampleRecordIds": [4, 5, 6]
                    }
                ]
            }
        }
    )
