"""
样品箱-瓶组关联相关数据传输对象
"""

from typing import Optional
from pydantic import BaseModel, Field


class SampleBoxBottleGroupLinkDTO(BaseModel):
    """样品箱-瓶组关联DTO"""
    sample_box_id: int = Field(..., description="样品箱ID")
    bottle_group_id: int = Field(..., description="瓶组ID")


class SampleBoxBottleGroupUnlinkDTO(BaseModel):
    """样品箱-瓶组取消关联DTO"""
    sample_box_id: int = Field(..., description="样品箱ID")
    bottle_group_id: int = Field(..., description="瓶组ID")