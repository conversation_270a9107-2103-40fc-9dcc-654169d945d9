"""
测试新的类别编码映射
验证更新后的映射关系是否正确
"""

import pytest
from utils.category_identifier_util import CategoryIdentifierUtil
from module_basedata.service.technical_manual_category_service import TechnicalManualCategoryService


class TestNewCategoryMapping:
    """测试新的类别编码映射"""

    def test_gas_category_mapping(self):
        """测试气体类 (Q) 的映射"""
        gas_categories = [
            "公共场所", "化学污染物", "物理因素",
            "气", "室内空气", "无组织废气", "有组织废气",
            "环境空气（小时值/一次值）", "环境空气（日均值）",
            "油气回收"
        ]
        
        for category in gas_categories:
            identifier = CategoryIdentifierUtil.get_identifier(category)
            assert identifier == "Q", f"分类 '{category}' 应该映射到 'Q'，实际得到 '{identifier}'"

    def test_solid_category_mapping(self):
        """测试固体类 (G) 的映射"""
        solid_categories = [
            "固", "固废（全量）", "固废（浸出）", "易燃性", "腐蚀性",
            "土", "土壤", "水系沉积物",
            "水处理剂", "活性炭（木质）", "活性炭（煤质）", "聚氯化铝",
            "水泥胶砂",
            "污泥", "医疗污泥", "城镇污泥",
            "海洋沉积物"
        ]
        
        for category in solid_categories:
            identifier = CategoryIdentifierUtil.get_identifier(category)
            assert identifier == "G", f"分类 '{category}' 应该映射到 'G'，实际得到 '{identifier}'"

    def test_water_category_mapping(self):
        """测试水体类 (S) 的映射"""
        water_categories = [
            "水", "地下水", "地表水", "大气降水", "废水", "生活饮用水",
            "海洋", "海水"
        ]
        
        for category in water_categories:
            identifier = CategoryIdentifierUtil.get_identifier(category)
            assert identifier == "S", f"分类 '{category}' 应该映射到 'S'，实际得到 '{identifier}'"

    def test_sound_category_mapping(self):
        """测试声学类 (Z) 的映射"""
        sound_categories = [
            "声", "24h噪声", "噪声（夜）", "噪声（昼）",
            "振动", "震动"  # 保持向后兼容
        ]
        
        for category in sound_categories:
            identifier = CategoryIdentifierUtil.get_identifier(category)
            assert identifier == "Z", f"分类 '{category}' 应该映射到 'Z'，实际得到 '{identifier}'"

    def test_ecology_category_mapping(self):
        """测试生态类 (B) 的映射"""
        ecology_categories = [
            "生态", "水生生态调查", "生物监测（定性）", "生物监测（定量）"
        ]
        
        for category in ecology_categories:
            identifier = CategoryIdentifierUtil.get_identifier(category)
            assert identifier == "B", f"分类 '{category}' 应该映射到 'B'，实际得到 '{identifier}'"

    def test_other_category_mapping(self):
        """测试其他类 (/) 的映射"""
        other_categories = [
            "能源", "辐射", "放射性物质", "电磁辐射", "电离辐射"
        ]

        for category in other_categories:
            identifier = CategoryIdentifierUtil.get_identifier(category)
            assert identifier == "/", f"分类 '{category}' 应该映射到 '/'，实际得到 '{identifier}'"

    def test_unknown_category_mapping(self):
        """测试未知分类的映射"""
        unknown_categories = [
            "未知分类", "不存在的分类", "", None
        ]

        for category in unknown_categories:
            identifier = CategoryIdentifierUtil.get_identifier(category)
            assert identifier == "/", f"未知分类 '{category}' 应该映射到 '/'，实际得到 '{identifier}'"

    def test_technical_manual_mapping_consistency(self):
        """测试技术手册映射与样品编号映射的一致性"""
        from unittest.mock import MagicMock

        # 创建模拟的数据库会话
        mock_db = MagicMock()
        service = TechnicalManualCategoryService(mock_db)

        # 测试一些关键分类的一致性
        test_cases = [
            ("公共场所", "Q"),
            ("气", "Q"),
            ("土", "G"),
            ("土壤", "G"),
            ("水", "S"),
            ("声", "Z"),
            ("振动", "Z"),
            ("生态", "B")
        ]

        for classification, expected_code in test_cases:
            # 样品编号映射
            sample_identifier = CategoryIdentifierUtil.get_identifier(classification)

            # 技术手册映射
            manual_code = service.generate_classification_code(classification)

            # 验证一致性
            assert sample_identifier == expected_code, f"样品编号映射: {classification} -> {sample_identifier}, 期望: {expected_code}"
            assert manual_code == expected_code, f"技术手册映射: {classification} -> {manual_code}, 期望: {expected_code}"

    def test_sample_number_examples(self):
        """测试样品编号生成示例"""
        task_code = "25010001"
        
        test_cases = [
            ("水", "S", "25010001S001"),
            ("土壤", "G", "25010001G001"),
            ("气", "Q", "25010001Q001"),
            ("声", "Z", "25010001Z001"),
            ("生态", "B", "25010001B001"),
            ("能源", "/", "25010001/001"),
            ("公共场所", "Q", "25010001Q001"),
            ("海水", "S", "25010001S001"),
            ("振动", "Z", "25010001Z001")
        ]
        
        for category, expected_identifier, expected_sample_number in test_cases:
            identifier = CategoryIdentifierUtil.get_identifier(category)
            sample_number = f"{task_code}{identifier}001"
            
            assert identifier == expected_identifier, f"分类 '{category}' 标识符错误"
            assert sample_number == expected_sample_number, f"分类 '{category}' 样品编号错误"

    def test_mapping_completeness(self):
        """测试映射的完整性"""
        all_mappings = CategoryIdentifierUtil.get_all_identifiers()
        
        # 验证所有映射都有值
        for classification, identifier in all_mappings.items():
            assert identifier is not None, f"分类 '{classification}' 的标识符不能为空"
            assert len(identifier) > 0, f"分类 '{classification}' 的标识符不能为空字符串"
            assert identifier in ["Q", "G", "S", "Z", "B", "/"], f"分类 '{classification}' 的标识符 '{identifier}' 不在允许的范围内"

    def test_backward_compatibility(self):
        """测试向后兼容性"""
        # 测试一些保持兼容的映射
        compatibility_cases = [
            ("震动", "Z"),  # 保持向后兼容，同时支持"振动"
            ("海洋", "S"),  # 保持向后兼容
        ]
        
        for classification, expected_identifier in compatibility_cases:
            identifier = CategoryIdentifierUtil.get_identifier(classification)
            assert identifier == expected_identifier, f"向后兼容性测试失败: {classification} -> {identifier}, 期望: {expected_identifier}"


if __name__ == "__main__":
    pytest.main([__file__])
