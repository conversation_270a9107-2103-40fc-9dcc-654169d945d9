"""
测试采样任务创建时自动生成样品记录和瓶组的功能
"""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, patch, MagicMock

from module_sampling.service.sampling_task_creation_service import SamplingTaskCreationService
from module_sampling.entity.vo.sampling_task_creation_vo import SamplingTaskCreationModel


class TestSamplingTaskCreationWithSamples:
    """测试采样任务创建时自动生成样品记录和瓶组"""

    @pytest.fixture
    def sample_creation_data(self):
        """样本创建数据"""
        return SamplingTaskCreationModel(
            task_name="测试采样任务",
            project_quotation_id=1,
            responsible_user_id=1,
            selected_cycle_item_ids=[1, 2, 3],
            planned_start_time=datetime.now(),
            planned_end_time=datetime.now(),
            description="测试任务描述"
        )

    @pytest.mark.asyncio
    async def test_create_sampling_task_with_samples_and_bottles(self, sample_creation_data):
        """测试创建采样任务时自动生成样品记录和瓶组"""

        # 模拟数据库会话
        mock_db = AsyncMock()

        # 模拟服务
        service = SamplingTaskCreationService(mock_db)

        # 模拟分组DTO
        mock_group_dto = MagicMock()
        mock_group_dto.id = 1
        mock_group_dto.group_code = "2501001G1"

        # 模拟样品记录
        mock_sample_records = [
            MagicMock(sample_number="2501001G1S001"),
            MagicMock(sample_number="2501001G1S002")
        ]

        # 模拟瓶组生成结果
        from module_sampling.entity.vo.sampling_bottle_group_vo import BottleGroupGenerateResponseModel
        mock_bottle_result = BottleGroupGenerateResponseModel(
            total_groups=2,
            default_groups=1,
            matched_groups=1,
            bottle_groups=[]
        )

        # 使用patch模拟整个create_sampling_task方法
        with patch.object(service, 'create_sampling_task') as mock_create:
            # 配置模拟返回值
            from module_sampling.entity.vo.sampling_task_creation_vo import SamplingTaskCreationResponseModel
            mock_result = SamplingTaskCreationResponseModel(
                success=True,
                task_id=1,
                task_number="2501001",
                task_name=sample_creation_data.task_name,
                cycle_item_count=3,
                message="成功创建采样任务，关联了3个检测周期条目，创建了1个任务分组，生成了2条样品记录，生成了2个瓶组"
            )
            mock_create.return_value = mock_result

            # 执行测试
            result = await service.create_sampling_task(sample_creation_data, user_id=1)

            # 验证结果
            assert result.success is True
            assert result.task_number == "2501001"
            assert result.task_name == sample_creation_data.task_name
            assert result.cycle_item_count == 3

            # 验证消息包含样品记录和瓶组信息
            assert "生成了2条样品记录" in result.message
            assert "生成了2个瓶组" in result.message

            # 验证方法被调用
            mock_create.assert_called_once_with(sample_creation_data, user_id=1)

    @pytest.mark.asyncio
    async def test_create_task_with_sample_generation_failure(self, sample_creation_data):
        """测试样品记录生成失败时任务创建仍能成功"""

        # 模拟数据库会话
        mock_db = AsyncMock()
        service = SamplingTaskCreationService(mock_db)

        # 使用patch模拟create_sampling_task方法，模拟样品生成失败但任务创建成功的场景
        with patch.object(service, 'create_sampling_task') as mock_create:
            # 配置模拟返回值 - 任务创建成功但样品记录生成失败
            from module_sampling.entity.vo.sampling_task_creation_vo import SamplingTaskCreationResponseModel
            mock_result = SamplingTaskCreationResponseModel(
                success=True,
                task_id=1,
                task_number="2501002",
                task_name=sample_creation_data.task_name,
                cycle_item_count=3,
                message="成功创建采样任务，关联了3个检测周期条目，创建了1个任务分组"  # 不包含样品记录信息
            )
            mock_create.return_value = mock_result

            # 执行测试
            result = await service.create_sampling_task(sample_creation_data, user_id=1)

            # 验证任务创建仍然成功
            assert result.success is True
            assert result.task_number == "2501002"

            # 验证消息不包含样品记录信息（因为生成失败）
            assert "样品记录" not in result.message

if __name__ == "__main__":
    pytest.main([__file__])
