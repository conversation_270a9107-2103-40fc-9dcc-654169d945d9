#!/usr/bin/env python3
"""
调试技术手册匹配逻辑问题
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_basedata.entity.do.technical_manual_do import TechnicalManual
from module_bottle_maintenance.entity.do.bottle_maintenance_do import BottleMaintenance
from module_bottle_maintenance.entity.do.bottle_maintenance_technical_manual_do import BottleMaintenanceTechnicalManual
from module_basedata.service.technical_manual_service import TechnicalManualService
from module_basedata.entity.vo.technical_manual_vo import TechnicalManualQueryModel


async def debug_technical_manual_matching():
    """调试技术手册匹配问题"""
    print("=" * 80)
    print("调试技术手册匹配逻辑问题")
    print("=" * 80)
    
    async for db in get_db():
        try:
            # 测试数据
            detection_category = "地下水"
            detection_parameter = "总锌, 钡, 钙, 钠, 镁, 1,2-二氯乙烷, 1,2-二氯丙烷, 三氯乙烯, pH值, 浊度, 六价铬"
            detection_method = "水质 32种元素的测定 电感耦合等离子体发射光谱法 HJ 776-2015, 水质 挥发性有机物的测定 吹扫捕集/气相色谱-质谱法 HJ 639-2012, 水质 pH值的测定 电极法 HJ 1147-2020, 水质 浊度的测定 浊度计法 HJ 1075-2019, 地下水质分析方法 第 17 部分：总铬和六价铬量的测定 二苯碳酰二肼分光光度法 DZ/T 0064.17-2021"
            
            print(f"检测类别: {detection_category}")
            print(f"检测参数: {detection_parameter}")
            print(f"检测方法: {detection_method}")
            print()
            
            # 1. 使用TechnicalManualService查询
            print("1. 使用TechnicalManualService查询技术手册...")
            service = TechnicalManualService(db)
            
            # 精确查询
            query_model = TechnicalManualQueryModel(
                parameter=detection_parameter,
                method=detection_method,
                category=detection_category,
                is_exact_query=True
            )
            
            technical_manual_list = await service.get_technical_manual_list(query_model)
            print(f"✅ 精确查询结果: 找到 {len(technical_manual_list)} 个技术手册")
            
            for manual in technical_manual_list:
                print(f"  技术手册ID: {manual.get('id')}")
                print(f"  参数: {manual.get('parameter')}")
                print(f"  方法: {manual.get('method')}")
                print(f"  类目编码: {manual.get('categoryCodes')}")
                print("-" * 40)
            
            # 2. 尝试分解检测参数和方法进行查询
            print("\n2. 分解检测参数和方法进行查询...")
            
            # 分解检测参数
            parameters = [p.strip() for p in detection_parameter.split(',')]
            methods = [m.strip() for m in detection_method.split(',')]
            
            print(f"分解后的检测参数 ({len(parameters)} 个):")
            for i, param in enumerate(parameters, 1):
                print(f"  {i}. {param}")
            
            print(f"\n分解后的检测方法 ({len(methods)} 个):")
            for i, method in enumerate(methods, 1):
                print(f"  {i}. {method}")
            
            # 3. 逐个查询每个参数-方法组合
            print("\n3. 逐个查询参数-方法组合...")
            
            found_manuals = []
            for param in parameters:
                for method in methods:
                    query_model = TechnicalManualQueryModel(
                        parameter=param,
                        method=method,
                        category=detection_category,
                        is_exact_query=True
                    )
                    
                    manual_list = await service.get_technical_manual_list(query_model)
                    if manual_list:
                        print(f"✅ 找到匹配: 参数='{param}', 方法='{method}'")
                        for manual in manual_list:
                            print(f"    技术手册ID: {manual.get('id')}")
                            found_manuals.extend(manual_list)
                    else:
                        print(f"❌ 未找到: 参数='{param}', 方法='{method}'")
            
            # 4. 查询所有相关的技术手册
            print(f"\n4. 查询包含任一参数的技术手册...")
            
            conditions = [TechnicalManual.status == "0"]
            param_conditions = []
            for param in parameters:
                param_conditions.append(TechnicalManual.parameter.like(f"%{param}%"))
            
            if param_conditions:
                conditions.append(or_(*param_conditions))
            
            stmt = select(TechnicalManual).where(and_(*conditions))
            result = await db.execute(stmt)
            all_manuals = result.scalars().all()
            
            print(f"✅ 找到包含任一参数的技术手册: {len(all_manuals)} 个")
            for manual in all_manuals:
                print(f"  ID: {manual.id}")
                print(f"  参数: {manual.parameter}")
                print(f"  方法: {manual.method}")
                print(f"  类目编码: {manual.category_codes}")
                
                # 查找关联的瓶组管理
                stmt = select(BottleMaintenance).join(
                    BottleMaintenanceTechnicalManual,
                    BottleMaintenance.id == BottleMaintenanceTechnicalManual.bottle_maintenance_id
                ).where(
                    BottleMaintenanceTechnicalManual.technical_manual_id == manual.id
                )
                result = await db.execute(stmt)
                bottle_maintenances = result.scalars().all()
                
                print(f"  关联的瓶组管理: {len(bottle_maintenances)} 个")
                for bm in bottle_maintenances:
                    print(f"    瓶组管理ID: {bm.id}, 编码: {bm.bottle_code}, 类型: {bm.bottle_type}")
                print("-" * 60)
            
            # 5. 分析问题
            print("\n5. 问题分析:")
            print("=" * 60)
            
            print("问题原因分析:")
            print("1. 样品记录中的检测参数和检测方法是多个值的组合字符串")
            print("2. 技术手册中的参数和方法通常是单个值")
            print("3. 当前的匹配逻辑使用精确匹配(==)，无法匹配组合字符串")
            print("4. 这导致相同的检测内容在不同时间生成瓶组时可能匹配到不同的结果")
            
            print("\n建议解决方案:")
            print("1. 修改瓶组匹配逻辑，将组合字符串分解为单个参数和方法")
            print("2. 对每个参数-方法组合分别进行匹配")
            print("3. 如果找到匹配的技术手册，使用对应的瓶组配置")
            print("4. 确保相同的检测内容始终使用相同的瓶组配置")
            
        except Exception as e:
            print(f"❌ 调试过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
        
        finally:
            await db.close()


if __name__ == "__main__":
    asyncio.run(debug_technical_manual_matching())
