# 任务编号格式更新

## 修改概述

将任务编号格式从**7位数字**（YYMM + 3位序号）更新为**8位数字**（YYMM + 4位序号），以支持更大的任务容量。

## 修改前后对比

### 修改前的格式

**格式**：`YYMMXXX`（7位数字）
- **YY**：年份后两位
- **MM**：月份两位  
- **XXX**：三位流水号（每月从001开始，最大999）

**示例**：
- `2501001`：2025年1月第1个任务
- `2501999`：2025年1月第999个任务（最大容量）

**容量限制**：每月最多999个任务

### 修改后的格式

**格式**：`YYMMXXXX`（8位数字）
- **YY**：年份后两位
- **MM**：月份两位
- **XXXX**：四位流水号（每月从0001开始，最大9999）

**示例**：
- `25010001`：2025年1月第1个任务
- `25019999`：2025年1月第9999个任务（最大容量）

**容量提升**：每月最多9999个任务（提升10倍）

## 技术实现

### 1. 修改DAO层代码

**文件**：`back/module_sampling/dao/sampling_task_dao.py`

```python
async def generate_task_code(self) -> str:
    """生成任务编号
    格式：YYMMXXXX（8位数字）
    YY：年份后两位
    MM：月份两位
    XXXX：四位流水号（每月从0001开始）
    """
    now = datetime.now()
    year_month = now.strftime('%y%m')  # 年份后两位 + 月份两位

    # 获取或创建序列记录
    sequence_result = await self.db.execute(
        select(SamplingTaskSequence).filter(
            SamplingTaskSequence.year_month == year_month
        )
    )
    sequence = sequence_result.scalars().first()

    if not sequence:
        sequence = SamplingTaskSequence(year_month=year_month, sequence_number=0)
        self.db.add(sequence)
        await self.db.flush()

    # 递增序列号
    sequence.sequence_number += 1
    await self.db.merge(sequence)
    await self.db.flush()

    # 生成任务编号：YYMMXXXX（8位数字）
    task_code = f"{year_month}{sequence.sequence_number:04d}"  # 改为4位格式化
    return task_code
```

**关键修改**：
- 注释中的格式说明从`YYMMXXX`改为`YYMMXXXX`
- 格式化代码从`:03d`改为`:04d`

### 2. 更新相关注释和文档

**文件**：`back/module_sampling/service/sample_record_service.py`

```python
async def _generate_sample_number(self, task_code: str, detection_category: str, sequence: int) -> str:
    """
    生成样品编号
    格式：任务编号 + 类别标识 + 样品序号（3位）
    例如：25010001S001（任务25010001的第1个水样品）

    Args:
        task_code: 任务编号（格式：YYMMXXXX，如：25010001）  # 更新格式说明
        detection_category: 检测类别（如：土壤、地表水等）
        sequence: 样品序号（从1开始，任务级别全局递增）

    Returns:
        完整的样品编号
    """
```

### 3. 更新测试用例

**文件**：`back/tests/test_sample_number_generation.py`

更新所有测试用例中的任务编号示例：
- `2501001` → `25010001`
- `2501002` → `25010002`
- `2501003` → `25010003`
- 等等...

## 实际示例

### 2025年1月任务编号

| 创建顺序 | 年月 | 序号 | 任务编号 |
|---------|------|------|----------|
| 第1个任务 | 2501 | 0001 | 25010001 |
| 第2个任务 | 2501 | 0002 | 25010002 |
| 第3个任务 | 2501 | 0003 | 25010003 |
| ... | ... | ... | ... |
| 第9999个任务 | 2501 | 9999 | 25019999 |

### 2025年2月任务编号

| 创建顺序 | 年月 | 序号 | 任务编号 |
|---------|------|------|----------|
| 2月第1个任务 | 2502 | 0001 | 25020001 |
| 2月第2个任务 | 2502 | 0002 | 25020002 |

### 样品编号示例

基于新的8位任务编号，样品编号示例：

| 任务编号 | 样品类型 | 样品序号 | 样品编号 |
|---------|---------|---------|----------|
| 25010001 | 水 | 001 | 25010001S001 |
| 25010001 | 水 | 002 | 25010001S002 |
| 25010001 | 土壤 | 003 | 25010001T003 |
| 25010002 | 气 | 001 | 25010002Q001 |

## 优势

### 1. 容量提升
- **修改前**：每月最多999个任务
- **修改后**：每月最多9999个任务
- **提升倍数**：10倍容量提升

### 2. 未来扩展性
- 支持业务快速增长
- 减少因容量不足导致的系统限制
- 为高峰期任务创建提供充足空间

### 3. 保持一致性
- 编号仍然按月分组
- 时间信息仍然可读
- 序号仍然有序递增

## 兼容性说明

### 1. 向后兼容
- 现有的7位任务编号继续有效
- 新创建的任务使用8位编号
- 系统可以同时处理两种格式

### 2. 数据库兼容
- `sampling_task_sequence` 表结构无需修改
- `sequence_number` 字段支持更大数值
- 现有序列记录继续有效

### 3. 接口兼容
- API接口无需修改
- 前端显示自动适应新格式
- 查询和过滤功能正常工作

## 测试验证

### 1. 单元测试更新
- 更新所有涉及任务编号的测试用例
- 验证新格式的生成逻辑
- 确保格式化正确（4位序号）

### 2. 集成测试
- 验证任务创建流程
- 验证样品编号生成
- 验证编号唯一性

## 部署说明

### 1. 代码部署
- 部署更新后的代码
- 无需数据库迁移
- 重启后端服务

### 2. 验证步骤
1. 创建新任务，验证编号格式为8位
2. 检查样品编号是否正确引用新的任务编号
3. 确认现有任务功能不受影响

## 总结

本次修改成功将任务编号格式从7位升级到8位，实现了：

1. ✅ 任务容量提升10倍（999 → 9999）
2. ✅ 保持向后兼容性
3. ✅ 无需数据库迁移
4. ✅ 测试用例全面更新
5. ✅ 文档同步更新

新的8位任务编号格式为系统的长期发展提供了更好的支持，同时保持了编号的可读性和系统性。
