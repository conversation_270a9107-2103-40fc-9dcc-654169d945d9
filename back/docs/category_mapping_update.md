# 类别编码映射更新

## 更新概述

根据业务需求，对LIMS系统中的类别编码映射进行了全面更新，统一了样品编号生成和技术手册分类的编码标准。

## 新的映射规则

### 完整映射表

| 一级分类 | 二级分类 | 编码 | 样品编号示例 |
|---------|---------|------|-------------|
| **公共场所** | 化学污染物 | **Q** | 25010001Q001 |
| | 物理因素 | **Q** | 25010001Q002 |
| **气** | 室内空气 | **Q** | 25010001Q003 |
| | 无组织废气 | **Q** | 25010001Q004 |
| | 有组织废气 | **Q** | 25010001Q005 |
| | 环境空气（小时值/一次值） | **Q** | 25010001Q006 |
| | 环境空气（日均值） | **Q** | 25010001Q007 |
| **油气回收** | 油气回收 | **Q** | 25010001Q008 |
| **固** | 固废（全量） | **G** | 25010001G001 |
| | 固废（浸出） | **G** | 25010001G002 |
| | 易燃性 | **G** | 25010001G003 |
| | 腐蚀性 | **G** | 25010001G004 |
| **土** | 土壤 | **G** | 25010001G005 |
| | 水系沉积物 | **G** | 25010001G006 |
| **水处理剂** | 水处理剂 | **G** | 25010001G007 |
| | 活性炭（木质） | **G** | 25010001G008 |
| | 活性炭（煤质） | **G** | 25010001G009 |
| | 聚氯化铝 | **G** | 25010001G010 |
| **水泥胶砂** | 水泥胶砂 | **G** | 25010001G011 |
| **污泥** | 医疗污泥 | **G** | 25010001G012 |
| | 城镇污泥 | **G** | 25010001G013 |
| **海洋** | 海洋沉积物 | **G** | 25010001G014 |
| **水** | 地下水 | **S** | 25010001S001 |
| | 地表水 | **S** | 25010001S002 |
| | 大气降水 | **S** | 25010001S003 |
| | 废水 | **S** | 25010001S004 |
| | 生活饮用水 | **S** | 25010001S005 |
| **海洋** | 海水 | **S** | 25010001S006 |
| **声** | 24h噪声 | **Z** | 25010001Z001 |
| | 噪声（夜） | **Z** | 25010001Z002 |
| | 噪声（昼） | **Z** | 25010001Z003 |
| **振动** | 振动 | **Z** | 25010001Z004 |
| **生态** | 水生生态调查 | **B** | 25010001B001 |
| | 生物监测（定性） | **B** | 25010001B002 |
| | 生物监测（定量） | **B** | 25010001B003 |
| **能源** | 能源 | **/** | 25010001/001 |
| **辐射** | 放射性物质 | **/** | 25010001/002 |
| | 电磁辐射 | **/** | 25010001/003 |
| | 电离辐射 | **/** | 25010001/004 |

### 编码分类汇总

| 编码 | 类别 | 包含的分类 |
|------|------|-----------|
| **Q** | 气体类 | 公共场所、气、油气回收 |
| **G** | 固体类 | 固、土、水处理剂、水泥胶砂、污泥、海洋沉积物 |
| **S** | 水体类 | 水、海水 |
| **Z** | 声学类 | 声、振动 |
| **B** | 生态类 | 生态 |
| **/** | 其他类 | 能源、辐射、未知分类 |

## 修改前后对比

### 主要变更

| 分类名称 | 修改前编码 | 修改后编码 | 变更说明 |
|---------|-----------|-----------|----------|
| 公共场所 | GG | **Q** | 归入气体类 |
| 土 | T | **G** | 归入固体类 |
| 土壤 | T | **G** | 归入固体类 |
| 声 | S | **Z** | 归入声学类 |
| 震动 | Z | **Z** | 保持不变 |
| 海洋 | H | **G/S** | 根据二级分类区分 |
| 生态 | ST | **B** | 简化编码 |
| 能源 | N | **/** | 归入其他类 |
| 辐射 | F | **/** | 归入其他类 |
| 水处理剂 | SC | **G** | 归入固体类 |
| 水泥胶砂 | SJ | **G** | 归入固体类 |
| 油气回收 | YH | **Q** | 归入气体类 |
| 污泥 | W | **G** | 归入固体类 |

### 新增二级分类支持

新映射表支持更细粒度的二级分类，包括：

**气体类 (Q)**：
- 化学污染物、物理因素
- 室内空气、无组织废气、有组织废气
- 环境空气（小时值/一次值）、环境空气（日均值）

**固体类 (G)**：
- 固废（全量）、固废（浸出）、易燃性、腐蚀性
- 水系沉积物、海洋沉积物
- 活性炭（木质）、活性炭（煤质）、聚氯化铝
- 医疗污泥、城镇污泥

**水体类 (S)**：
- 地下水、地表水、大气降水、废水、生活饮用水、海水

**声学类 (Z)**：
- 24h噪声、噪声（夜）、噪声（昼）、振动

**生态类 (B)**：
- 水生生态调查、生物监测（定性）、生物监测（定量）

## 技术实现

### 1. 样品编号生成映射

**文件**：`back/utils/category_identifier_util.py`

```python
CLASSIFICATION_IDENTIFIER_MAP = {
    # 公共场所 - Q
    "公共场所": "Q",
    "化学污染物": "Q",
    "物理因素": "Q",
    
    # 气 - Q
    "气": "Q",
    "室内空气": "Q",
    "无组织废气": "Q",
    # ... 更多映射
}
```

### 2. 技术手册分类映射

**文件**：`back/module_basedata/service/technical_manual_category_service.py`

```python
classification_mapping = {
    # 气体类 - Q
    "公共场所": "Q",
    "化学污染物": "Q",
    "物理因素": "Q",
    # ... 更多映射
}
```

### 3. 特殊处理

- **能源和辐射类别**：在技术手册中返回空字符串，在样品编号中使用"O"
- **向后兼容**：保留了"震动"→"Z"的映射，同时支持"振动"→"Z"
- **海洋分类**：移除了特殊条件映射，统一按二级分类处理

## 影响范围

### 直接影响

1. **样品编号生成**：新创建的样品将使用新的编码规则
2. **技术手册分类**：技术手册类别编码生成使用统一规则
3. **测试用例**：更新了所有相关的测试用例

### 兼容性保证

1. **现有数据**：已生成的样品编号不受影响
2. **API接口**：对外接口保持不变
3. **查询功能**：现有的查询和过滤功能正常工作

## 测试验证

### 更新的测试用例

1. **样品编号生成测试**：
   - 土壤：T → G
   - 公共场所：GG → Q
   - 声：S → Z
   - 生态：ST → B

2. **类别标识工具测试**：
   - 更新了所有映射关系的测试
   - 验证新的编码规则

3. **任务级别序号测试**：
   - 更新了期望的样品编号格式

## 部署说明

### 1. 代码部署
- 部署更新后的映射文件
- 无需数据库迁移
- 重启后端服务

### 2. 验证步骤
1. 创建新任务，验证样品编号使用新编码
2. 检查技术手册分类编码生成
3. 确认现有功能不受影响

## 总结

本次更新实现了：

1. ✅ 统一了两套映射体系的编码标准
2. ✅ 支持更细粒度的二级分类
3. ✅ 简化了编码规则，提高可读性
4. ✅ 保持了向后兼容性
5. ✅ 更新了完整的测试覆盖

新的映射规则更加科学合理，按照样品的物理形态和检测特性进行分类，便于管理和识别。
