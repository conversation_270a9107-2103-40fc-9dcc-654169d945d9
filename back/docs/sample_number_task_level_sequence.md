# 样品编号任务级别序号优化

## 修改概述

本次修改将样品编号的序号生成逻辑从**分组维度**调整为**任务维度**，确保同一个任务的所有样品编号序号都是连续递增的。

## 修改前后对比

### 修改前的逻辑

**样品编号格式**：`分组编号 + 类别标识 + 样品序号`

**示例**：
- 任务 25010001，分组 G1：`25010001G1S001`, `25010001G1S002`
- 任务 25010001，分组 G2：`25010001G2S001`, `25010001G2S002` （序号重新从001开始）

**问题**：每个分组的样品序号都从001开始，导致序号不连续。

### 修改后的逻辑

**样品编号格式**：`任务编号 + 类别标识 + 样品序号`

**示例**：
- 任务 25010001，分组 G1：`25010001S001`, `25010001S002`
- 任务 25010001，分组 G2：`25010001S003`, `25010001S004` （序号连续递增）

**优势**：
1. 任务级别的序号连续递增
2. 样品编号更简洁（去掉了分组标识）
3. 便于任务级别的样品统计和管理

## 技术实现

### 1. 新增数据库表

创建了 `sample_record_sequence` 表来管理任务级别的样品序号：

```sql
CREATE TABLE `sample_record_sequence` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sampling_task_id` bigint NOT NULL COMMENT '采样任务ID',
  `current_sequence` int NOT NULL DEFAULT 0 COMMENT '当前序号',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_sample_sequence` (`sampling_task_id`),
  CONSTRAINT `fk_sample_sequence_task` FOREIGN KEY (`sampling_task_id`) 
    REFERENCES `sampling_task` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='样品记录序号表';
```

### 2. 新增实体类

**文件**：`back/module_sampling/entity/do/sample_record_sequence_do.py`

```python
class SampleRecordSequence(Base):
    """样品记录序号表"""
    __tablename__ = 'sample_record_sequence'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='主键ID')
    sampling_task_id = Column(BigInteger, ForeignKey('sampling_task.id'), nullable=False, comment='采样任务ID')
    current_sequence = Column(Integer, default=0, comment='当前序号')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
```

### 3. 修改DAO层

**文件**：`back/module_sampling/dao/sample_record_dao.py`

新增方法：
```python
async def get_next_sample_sequence(self, task_id: int) -> int:
    """获取任务的下一个样品序号"""
    # 查找现有序列记录
    stmt = select(SampleRecordSequence).where(SampleRecordSequence.sampling_task_id == task_id)
    result = await self.db.execute(stmt)
    sequence_record = result.scalar_one_or_none()

    if sequence_record:
        # 更新序列号
        sequence_record.current_sequence += 1
        await self.db.flush()
        return sequence_record.current_sequence
    else:
        # 创建新的序列记录
        new_sequence = SampleRecordSequence(
            sampling_task_id=task_id,
            current_sequence=1
        )
        self.db.add(new_sequence)
        await self.db.flush()
        return 1
```

### 4. 修改服务层

**文件**：`back/module_sampling/service/sample_record_service.py`

#### 修改样品记录生成逻辑：

```python
# 修改前
group_code = group.group_code
for i in range(max_sample_count):
    sample_number = await self._generate_sample_number(group_code, detection_category, i + 1)

# 修改后
task_code = group.sampling_task.task_code
for i in range(max_sample_count):
    sample_sequence = await self._get_next_sample_sequence(group.sampling_task_id)
    sample_number = await self._generate_sample_number(task_code, detection_category, sample_sequence)
```

#### 修改样品编号生成方法：

```python
async def _generate_sample_number(self, task_code: str, detection_category: str, sequence: int) -> str:
    """
    生成样品编号
    格式：任务编号 + 类别标识 + 样品序号（3位）
    例如：25010001S001（任务25010001的第1个水样品）
    """
    # ... 获取类别标识逻辑 ...
    
    # 生成样品编号：任务编号 + 类别标识 + 样品序号（3位）
    sample_number = f"{task_code}{category_identifier}{sequence:03d}"
    return sample_number
```

#### 新增序号获取方法：

```python
async def _get_next_sample_sequence(self, task_id: int) -> int:
    """获取任务的下一个样品序号"""
    return await self.sample_record_dao.get_next_sample_sequence(task_id)
```

## 数据库迁移

**文件**：`back/migrations/20250103_create_sample_record_sequence_table.sql`

该迁移文件会：
1. 创建 `sample_record_sequence` 表
2. 为现有任务初始化序号记录

## 测试验证

### 1. 单元测试

**文件**：`back/tests/test_sample_number_generation.py`

新增测试用例：
- `test_get_next_sample_sequence`：测试获取下一个样品序号
- `test_task_level_sequence_logic`：测试任务级别序号逻辑

### 2. 集成测试

**文件**：`back/tests/test_sampling_task_creation_with_samples.py`

验证任务创建时样品记录生成功能正常。

## 兼容性说明

1. **向后兼容**：现有的样品记录不受影响
2. **数据迁移**：为现有任务自动创建序号记录
3. **API兼容**：对外接口保持不变

## 影响范围

### 直接影响
- 样品编号生成逻辑
- 样品记录创建流程

### 间接影响
- 样品编号显示格式
- 样品统计和查询逻辑

## 部署说明

1. 执行数据库迁移脚本
2. 重启后端服务
3. 验证新创建的任务样品编号格式

## 总结

本次修改成功将样品编号序号从分组维度调整为任务维度，实现了：

1. ✅ 任务级别的序号连续递增
2. ✅ 样品编号格式简化
3. ✅ 保持向后兼容性
4. ✅ 完整的测试覆盖
5. ✅ 数据库迁移支持

修改后的样品编号更加简洁和连续，便于任务级别的样品管理和统计。
