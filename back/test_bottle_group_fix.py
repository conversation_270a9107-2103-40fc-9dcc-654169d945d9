#!/usr/bin/env python3
"""
测试瓶组匹配逻辑修复
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.database import AsyncSessionLocal
from module_bottle_maintenance.service.bottle_maintenance_service import BottleMaintenanceService
from module_basedata.service.technical_manual_service import TechnicalManualService


async def test_bottle_matching():
    """测试瓶组匹配逻辑"""
    print("=" * 80)
    print("测试瓶组匹配逻辑修复")
    print("=" * 80)
    
    async with AsyncSessionLocal() as db:
        # 初始化服务
        bottle_maintenance_service = BottleMaintenanceService(db)
        
        # 测试数据（来自实际问题样品）
        detection_category = "地下水"
        detection_parameter = "总锌, 钡, 钙, 钠, 镁, 1,2-二氯乙烷, 1,2-二氯丙烷, 三氯乙烯, pH值, 浊度, 六价铬"
        detection_method = "水质 32种元素的测定 电感耦合等离子体发射光谱法 HJ 776-2015, 水质 挥发性有机物的测定 吹扫捕集/气相色谱-质谱法 HJ 639-2012, 水质 pH值的测定 电极法 HJ 1147-2020, 水质 浊度的测定 浊度计法 HJ 1075-2019, 地下水质分析方法 第 17 部分：总铬和六价铬量的测定 二苯碳酰二肼分光光度法 DZ/T 0064.17-2021"
        
        print(f"检测类别: {detection_category}")
        print(f"检测参数: {detection_parameter}")
        print(f"检测方法: {detection_method}")
        print()
        
        # 测试修复后的匹配逻辑
        print("1. 测试修复后的瓶组匹配逻辑...")
        bottle_maintenance = await bottle_maintenance_service.get_bottle_by_detection_triple(
            detection_category, detection_parameter, detection_method
        )
        
        if bottle_maintenance:
            print(f"✅ 找到匹配的瓶组:")
            print(f"    瓶组ID: {bottle_maintenance.id}")
            print(f"    瓶组编码: {bottle_maintenance.bottle_code}")
            print(f"    瓶组类型: {bottle_maintenance.bottle_type}")
            print(f"    瓶组容量: {bottle_maintenance.bottle_volume}")
            print(f"    存储方式: {bottle_maintenance.storage_styles}")
        else:
            print("❌ 未找到匹配的瓶组")
        
        print()
        
        # 测试多次调用的一致性
        print("2. 测试多次调用的一致性...")
        results = []
        for i in range(5):
            result = await bottle_maintenance_service.get_bottle_by_detection_triple(
                detection_category, detection_parameter, detection_method
            )
            bottle_id = result.id if result else None
            results.append(bottle_id)
            print(f"    第{i+1}次调用: 瓶组ID = {bottle_id}")
        
        # 检查一致性
        unique_results = set(results)
        if len(unique_results) == 1:
            print("✅ 多次调用结果一致")
        else:
            print(f"❌ 多次调用结果不一致: {unique_results}")
        
        print()
        
        # 测试单个参数匹配
        print("3. 测试单个参数匹配...")
        single_param_tests = [
            ("总锌", "水质 32种元素的测定 电感耦合等离子体发射光谱法 HJ 776-2015"),
            ("钡", "水质 32种元素的测定 电感耦合等离子体发射光谱法 HJ 776-2015"),
            ("pH值", "水质 pH值的测定 电极法 HJ 1147-2020"),
            ("六价铬", "地下水质分析方法 第 17 部分：总铬和六价铬量的测定 二苯碳酰二肼分光光度法 DZ/T 0064.17-2021")
        ]
        
        for param, method in single_param_tests:
            result = await bottle_maintenance_service.get_bottle_by_detection_triple(
                detection_category, param, method
            )
            bottle_id = result.id if result else None
            print(f"    参数='{param}' -> 瓶组ID = {bottle_id}")


if __name__ == "__main__":
    asyncio.run(test_bottle_matching())
