"""
合同财务管理服务
"""

from datetime import datetime
from typing import List, Optional
from decimal import Decimal

from sqlalchemy import select, and_, func
from sqlalchemy.ext.asyncio import AsyncSession

from exceptions.exception import ServiceException
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_contract.entity.do.contract_do import Contract
from module_contract.entity.do.contract_financial_do import (
    ContractInvoice,
    ContractPayment,
    ContractReceipt,
    ContractCostPayment,
)
from module_contract.entity.vo.contract_financial_vo import (
    ContractInvoiceModel,
    ContractPaymentModel,
    ContractReceiptModel,
    ContractCostPaymentModel,
    ContractFinancialSummaryModel,
    ContractPaymentSummaryModel,
    ContractCostSummaryModel,
)
from utils.common_util import CamelCaseUtil


class ContractFinancialService:
    """合同财务管理服务"""

    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_financial_summary(self, contract_id: int) -> ContractFinancialSummaryModel:
        """
        获取合同财务汇总信息

        :param contract_id: 合同ID
        :return: 财务汇总信息
        """
        # 获取合同基本信息
        contract = await self._get_contract_by_id(contract_id)
        if not contract:
            raise ServiceException(message=f"合同ID：{contract_id}不存在")

        # 计算开票总金额
        invoice_stmt = select(func.coalesce(func.sum(ContractInvoice.invoice_amount), 0)).where(
            ContractInvoice.contract_id == contract_id
        )
        invoice_result = await self.db.execute(invoice_stmt)
        total_invoice_amount = invoice_result.scalar() or Decimal('0')

        # 计算回款总金额
        payment_stmt = select(func.coalesce(func.sum(ContractPayment.payment_amount), 0)).where(
            ContractPayment.contract_id == contract_id
        )
        payment_result = await self.db.execute(payment_stmt)
        total_payment_amount = payment_result.scalar() or Decimal('0')

        # 计算收票总金额
        receipt_stmt = select(func.coalesce(func.sum(ContractReceipt.receipt_amount), 0)).where(
            ContractReceipt.contract_id == contract_id
        )
        receipt_result = await self.db.execute(receipt_stmt)
        total_receipt_amount = receipt_result.scalar() or Decimal('0')

        # 计算付款总金额
        cost_payment_stmt = select(func.coalesce(func.sum(ContractCostPayment.payment_amount), 0)).where(
            ContractCostPayment.contract_id == contract_id
        )
        cost_payment_result = await self.db.execute(cost_payment_stmt)
        total_cost_payment_amount = cost_payment_result.scalar() or Decimal('0')

        return ContractFinancialSummaryModel(
            contract_id=contract_id,
            contract_amount=contract.contract_amount,
            contract_cost=contract.contract_cost,
            total_invoice_amount=total_invoice_amount,
            total_payment_amount=total_payment_amount,
            total_receipt_amount=total_receipt_amount,
            total_cost_payment_amount=total_cost_payment_amount,
        )

    async def get_payment_summary(self, contract_id: int) -> ContractPaymentSummaryModel:
        """
        获取合同回款汇总信息

        :param contract_id: 合同ID
        :return: 回款汇总信息
        """
        # 获取合同基本信息
        contract = await self._get_contract_by_id(contract_id)
        if not contract:
            raise ServiceException(message=f"合同ID：{contract_id}不存在")

        # 获取开票记录
        invoice_stmt = select(ContractInvoice).where(
            ContractInvoice.contract_id == contract_id
        ).order_by(ContractInvoice.invoice_date.desc())
        invoice_result = await self.db.execute(invoice_stmt)
        invoices = invoice_result.scalars().all()

        # 获取回款记录
        payment_stmt = select(ContractPayment).where(
            ContractPayment.contract_id == contract_id
        ).order_by(ContractPayment.payment_date.desc())
        payment_result = await self.db.execute(payment_stmt)
        payments = payment_result.scalars().all()

        # 计算汇总金额
        total_invoice_amount = sum(invoice.invoice_amount for invoice in invoices)
        total_payment_amount = sum(payment.payment_amount for payment in payments)

        return ContractPaymentSummaryModel(
            contract_amount=contract.contract_amount,
            total_invoice_amount=total_invoice_amount,
            total_payment_amount=total_payment_amount,
            invoices=[ContractInvoiceModel(**CamelCaseUtil.transform_result(invoice)) for invoice in invoices],
            payments=[ContractPaymentModel(**CamelCaseUtil.transform_result(payment)) for payment in payments],
        )

    async def get_cost_summary(self, contract_id: int) -> ContractCostSummaryModel:
        """
        获取合同成本汇总信息

        :param contract_id: 合同ID
        :return: 成本汇总信息
        """
        # 获取合同基本信息
        contract = await self._get_contract_by_id(contract_id)
        if not contract:
            raise ServiceException(message=f"合同ID：{contract_id}不存在")

        # 获取收票记录
        receipt_stmt = select(ContractReceipt).where(
            ContractReceipt.contract_id == contract_id
        ).order_by(ContractReceipt.receipt_date.desc())
        receipt_result = await self.db.execute(receipt_stmt)
        receipts = receipt_result.scalars().all()

        # 获取付款记录
        cost_payment_stmt = select(ContractCostPayment).where(
            ContractCostPayment.contract_id == contract_id
        ).order_by(ContractCostPayment.payment_date.desc())
        cost_payment_result = await self.db.execute(cost_payment_stmt)
        cost_payments = cost_payment_result.scalars().all()

        # 计算汇总金额
        total_receipt_amount = sum(receipt.receipt_amount for receipt in receipts)
        total_cost_payment_amount = sum(payment.payment_amount for payment in cost_payments)

        return ContractCostSummaryModel(
            contract_cost=contract.contract_cost,
            total_receipt_amount=total_receipt_amount,
            total_cost_payment_amount=total_cost_payment_amount,
            receipts=[ContractReceiptModel(**CamelCaseUtil.transform_result(receipt)) for receipt in receipts],
            cost_payments=[ContractCostPaymentModel(**CamelCaseUtil.transform_result(payment)) for payment in cost_payments],
        )

    async def add_invoice(self, invoice_model: ContractInvoiceModel, current_user: CurrentUserModel) -> CrudResponseModel:
        """
        新增开票记录

        :param invoice_model: 开票记录模型
        :param current_user: 当前用户
        :return: 新增结果
        """
        try:
            # 验证合同是否存在
            contract = await self._get_contract_by_id(invoice_model.contract_id)
            if not contract:
                raise ServiceException(message=f"合同ID：{invoice_model.contract_id}不存在")

            # 验证开票金额不超过合同金额
            await self._validate_invoice_amount(invoice_model.contract_id, invoice_model.invoice_amount, contract.contract_amount)

            # 创建开票记录
            invoice = ContractInvoice(
                contract_id=invoice_model.contract_id,
                invoice_amount=invoice_model.invoice_amount,
                invoice_date=invoice_model.invoice_date,
                invoice_number=invoice_model.invoice_number,
                invoice_type=invoice_model.invoice_type,
                tax_rate=invoice_model.tax_rate,
                tax_amount=invoice_model.tax_amount,
                create_by=current_user.user.user_name if current_user and current_user.user else "",
                create_time=datetime.now(),
                update_by=current_user.user.user_name if current_user and current_user.user else "",
                update_time=datetime.now(),
                remark=invoice_model.remark,
            )

            self.db.add(invoice)
            await self.db.flush()
            await self.db.commit()

            return CrudResponseModel(is_success=True, message="新增成功", result={"id": invoice.id})

        except Exception as e:
            await self.db.rollback()
            raise

    async def add_payment(self, payment_model: ContractPaymentModel, current_user: CurrentUserModel) -> CrudResponseModel:
        """
        新增回款记录

        :param payment_model: 回款记录模型
        :param current_user: 当前用户
        :return: 新增结果
        """
        try:
            # 验证合同是否存在
            contract = await self._get_contract_by_id(payment_model.contract_id)
            if not contract:
                raise ServiceException(message=f"合同ID：{payment_model.contract_id}不存在")

            # 验证回款金额不超过合同金额
            await self._validate_payment_amount(payment_model.contract_id, payment_model.payment_amount, contract.contract_amount)

            # 创建回款记录
            payment = ContractPayment(
                contract_id=payment_model.contract_id,
                payment_amount=payment_model.payment_amount,
                payment_date=payment_model.payment_date,
                payment_method=payment_model.payment_method,
                payment_account=payment_model.payment_account,
                bank_info=payment_model.bank_info,
                create_by=current_user.user.user_name if current_user and current_user.user else "",
                create_time=datetime.now(),
                update_by=current_user.user.user_name if current_user and current_user.user else "",
                update_time=datetime.now(),
                remark=payment_model.remark,
            )

            self.db.add(payment)
            await self.db.flush()
            await self.db.commit()

            return CrudResponseModel(is_success=True, message="新增成功", result={"id": payment.id})

        except Exception as e:
            await self.db.rollback()
            raise

    async def _get_contract_by_id(self, contract_id: int) -> Optional[Contract]:
        """
        根据ID获取合同

        :param contract_id: 合同ID
        :return: 合同对象
        """
        stmt = select(Contract).where(Contract.id == contract_id)
        result = await self.db.execute(stmt)
        return result.scalars().first()

    async def _validate_invoice_amount(self, contract_id: int, new_amount: Decimal, contract_amount: Decimal, exclude_id: Optional[int] = None):
        """
        验证开票金额是否超过合同金额

        :param contract_id: 合同ID
        :param new_amount: 新增金额
        :param contract_amount: 合同金额
        :param exclude_id: 排除的记录ID（用于编辑时）
        """
        conditions = [
            ContractInvoice.contract_id == contract_id
        ]
        if exclude_id:
            conditions.append(ContractInvoice.id != exclude_id)

        stmt = select(func.coalesce(func.sum(ContractInvoice.invoice_amount), 0)).where(and_(*conditions))
        result = await self.db.execute(stmt)
        current_total = result.scalar() or Decimal('0')

        if current_total + new_amount > contract_amount:
            raise ServiceException(message=f"开票总金额不能超过合同金额{contract_amount}")

    async def _validate_payment_amount(self, contract_id: int, new_amount: Decimal, contract_amount: Decimal, exclude_id: Optional[int] = None):
        """
        验证回款金额是否超过合同金额

        :param contract_id: 合同ID
        :param new_amount: 新增金额
        :param contract_amount: 合同金额
        :param exclude_id: 排除的记录ID（用于编辑时）
        """
        conditions = [
            ContractPayment.contract_id == contract_id
        ]
        if exclude_id:
            conditions.append(ContractPayment.id != exclude_id)

        stmt = select(func.coalesce(func.sum(ContractPayment.payment_amount), 0)).where(and_(*conditions))
        result = await self.db.execute(stmt)
        current_total = result.scalar() or Decimal('0')

        if current_total + new_amount > contract_amount:
            raise ServiceException(message=f"回款总金额不能超过合同金额{contract_amount}")

    async def add_receipt(self, receipt_model: ContractReceiptModel, current_user: CurrentUserModel) -> CrudResponseModel:
        """
        新增收票记录

        :param receipt_model: 收票记录模型
        :param current_user: 当前用户
        :return: 新增结果
        """
        try:
            # 验证合同是否存在
            contract = await self._get_contract_by_id(receipt_model.contract_id)
            if not contract:
                raise ServiceException(message=f"合同ID：{receipt_model.contract_id}不存在")

            # 验证收票金额不超过合同成本
            if contract.contract_cost:
                await self._validate_receipt_amount(receipt_model.contract_id, receipt_model.receipt_amount, contract.contract_cost)

            # 创建收票记录
            receipt = ContractReceipt(
                contract_id=receipt_model.contract_id,
                receipt_amount=receipt_model.receipt_amount,
                receipt_date=receipt_model.receipt_date,
                receipt_number=receipt_model.receipt_number,
                receipt_type=receipt_model.receipt_type,
                supplier_name=receipt_model.supplier_name,
                create_by=current_user.user.user_name if current_user and current_user.user else "",
                create_time=datetime.now(),
                update_by=current_user.user.user_name if current_user and current_user.user else "",
                update_time=datetime.now(),
                remark=receipt_model.remark,
            )

            self.db.add(receipt)
            await self.db.flush()
            await self.db.commit()

            return CrudResponseModel(is_success=True, message="新增成功", result={"id": receipt.id})

        except Exception as e:
            await self.db.rollback()
            raise

    async def add_cost_payment(self, cost_payment_model: ContractCostPaymentModel, current_user: CurrentUserModel) -> CrudResponseModel:
        """
        新增付款记录

        :param cost_payment_model: 付款记录模型
        :param current_user: 当前用户
        :return: 新增结果
        """
        try:
            # 验证合同是否存在
            contract = await self._get_contract_by_id(cost_payment_model.contract_id)
            if not contract:
                raise ServiceException(message=f"合同ID：{cost_payment_model.contract_id}不存在")

            # 验证付款金额不超过合同成本
            if contract.contract_cost:
                await self._validate_cost_payment_amount(cost_payment_model.contract_id, cost_payment_model.payment_amount, contract.contract_cost)

            # 创建付款记录
            cost_payment = ContractCostPayment(
                contract_id=cost_payment_model.contract_id,
                payment_amount=cost_payment_model.payment_amount,
                payment_date=cost_payment_model.payment_date,
                payment_method=cost_payment_model.payment_method,
                payee_name=cost_payment_model.payee_name,
                payee_account=cost_payment_model.payee_account,
                bank_info=cost_payment_model.bank_info,
                create_by=current_user.user.user_name if current_user and current_user.user else "",
                create_time=datetime.now(),
                update_by=current_user.user.user_name if current_user and current_user.user else "",
                update_time=datetime.now(),
                remark=cost_payment_model.remark,
            )

            self.db.add(cost_payment)
            await self.db.flush()
            await self.db.commit()

            return CrudResponseModel(is_success=True, message="新增成功", result={"id": cost_payment.id})

        except Exception as e:
            await self.db.rollback()
            raise

    async def _validate_receipt_amount(self, contract_id: int, new_amount: Decimal, contract_cost: Decimal, exclude_id: Optional[int] = None):
        """
        验证收票金额是否超过合同成本

        :param contract_id: 合同ID
        :param new_amount: 新增金额
        :param contract_cost: 合同成本
        :param exclude_id: 排除的记录ID（用于编辑时）
        """
        conditions = [
            ContractReceipt.contract_id == contract_id
        ]
        if exclude_id:
            conditions.append(ContractReceipt.id != exclude_id)

        stmt = select(func.coalesce(func.sum(ContractReceipt.receipt_amount), 0)).where(and_(*conditions))
        result = await self.db.execute(stmt)
        current_total = result.scalar() or Decimal('0')

        if current_total + new_amount > contract_cost:
            raise ServiceException(message=f"收票总金额不能超过合同成本{contract_cost}")

    async def _validate_cost_payment_amount(self, contract_id: int, new_amount: Decimal, contract_cost: Decimal, exclude_id: Optional[int] = None):
        """
        验证付款金额是否超过合同成本

        :param contract_id: 合同ID
        :param new_amount: 新增金额
        :param contract_cost: 合同成本
        :param exclude_id: 排除的记录ID（用于编辑时）
        """
        conditions = [
            ContractCostPayment.contract_id == contract_id
        ]
        if exclude_id:
            conditions.append(ContractCostPayment.id != exclude_id)

        stmt = select(func.coalesce(func.sum(ContractCostPayment.payment_amount), 0)).where(and_(*conditions))
        result = await self.db.execute(stmt)
        current_total = result.scalar() or Decimal('0')

        if current_total + new_amount > contract_cost:
            raise ServiceException(message=f"付款总金额不能超过合同成本{contract_cost}")
