-- 创建样品记录序号表
-- 用于管理每个任务的样品序号，确保任务级别的序号连续递增

CREATE TABLE IF NOT EXISTS `sample_record_sequence` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sampling_task_id` bigint NOT NULL COMMENT '采样任务ID',
  `current_sequence` int NOT NULL DEFAULT 0 COMMENT '当前序号',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_sample_sequence` (`sampling_task_id`),
  CONSTRAINT `fk_sample_sequence_task` FOREIGN KEY (`sampling_task_id`) 
    REFERENCES `sampling_task` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='样品记录序号表';

-- 为现有任务初始化序号记录（如果有的话）
-- 这个脚本会为每个现有的采样任务创建一个序号记录，初始序号设为0
INSERT INTO `sample_record_sequence` (`sampling_task_id`, `current_sequence`)
SELECT `id`, 0 FROM `sampling_task`
WHERE NOT EXISTS (
    SELECT 1 FROM `sample_record_sequence` 
    WHERE `sample_record_sequence`.`sampling_task_id` = `sampling_task`.`id`
);
