-- 样品箱管理相关表创建脚本
-- 创建时间：2024-11-04
-- 描述：创建样品箱管理功能所需的数据库表

-- 1. 创建样品箱表
CREATE TABLE `sample_box` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sampling_task_id` bigint NOT NULL COMMENT '采样任务ID',
  `box_code` varchar(100) NOT NULL COMMENT '样品箱编号（格式：XP+YYYYMMDD+序号）',
  `box_name` varchar(200) DEFAULT NULL COMMENT '样品箱名称',
  `description` text COMMENT '样品箱描述',
  `sample_count` int DEFAULT '0' COMMENT '样品数量',
  `status` int DEFAULT '0' COMMENT '状态：0-新建，1-已封箱，2-已发送',
  `is_sealed` tinyint(1) DEFAULT '0' COMMENT '是否已封箱',
  `sealed_time` datetime DEFAULT NULL COMMENT '封箱时间',
  `sealed_by` bigint DEFAULT NULL COMMENT '封箱人',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_box_code` (`box_code`),
  KEY `idx_sample_box_task_id` (`sampling_task_id`),
  KEY `idx_sample_box_code` (`box_code`),
  KEY `idx_sample_box_status` (`status`),
  KEY `idx_sample_box_create_time` (`create_time`),
  CONSTRAINT `fk_sample_box_sampling_task` FOREIGN KEY (`sampling_task_id`) REFERENCES `sampling_task` (`id`),
  CONSTRAINT `fk_sample_box_create_by` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`user_id`),
  CONSTRAINT `fk_sample_box_update_by` FOREIGN KEY (`update_by`) REFERENCES `sys_user` (`user_id`),
  CONSTRAINT `fk_sample_box_sealed_by` FOREIGN KEY (`sealed_by`) REFERENCES `sys_user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='样品箱表';

-- 2. 创建样品箱样品关联表
CREATE TABLE `sample_box_sample` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sample_box_id` bigint NOT NULL COMMENT '样品箱ID',
  `sample_record_id` bigint NOT NULL COMMENT '样品记录ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_box_sample` (`sample_box_id`,`sample_record_id`),
  KEY `idx_sample_box_sample_box_id` (`sample_box_id`),
  KEY `idx_sample_box_sample_record_id` (`sample_record_id`),
  CONSTRAINT `fk_sample_box_sample_box` FOREIGN KEY (`sample_box_id`) REFERENCES `sample_box` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sample_box_sample_record` FOREIGN KEY (`sample_record_id`) REFERENCES `sample_record` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_sample_box_sample_create_by` FOREIGN KEY (`create_by`) REFERENCES `sys_user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='样品箱样品关联表';

-- 3. 创建样品箱编号序列表
CREATE TABLE `sample_box_sequence` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `date_key` varchar(8) NOT NULL COMMENT '日期键（YYYYMMDD格式）',
  `sequence_number` int DEFAULT '0' COMMENT '当日序号',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_date_key` (`date_key`),
  KEY `idx_sample_box_sequence_date` (`date_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='样品箱编号序列表';

-- 4. 插入初始数据
INSERT INTO sample_box_sequence (date_key, sequence_number) 
VALUES (DATE_FORMAT(NOW(), '%Y%m%d'), 0) 
ON DUPLICATE KEY UPDATE sequence_number = sequence_number;

-- 5. 验证表创建成功
SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    ENGINE,
    TABLE_COLLATION
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME IN ('sample_box', 'sample_box_sample', 'sample_box_sequence');

-- 6. 验证字段创建成功
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME IN ('sample_box', 'sample_box_sample', 'sample_box_sequence')
ORDER BY TABLE_NAME, ORDINAL_POSITION;
