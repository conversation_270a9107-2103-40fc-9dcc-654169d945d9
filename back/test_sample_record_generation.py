#!/usr/bin/env python3
"""
测试样品记录生成问题
"""

import asyncio
import sys
import traceback
sys.path.append('.')

from config.database import AsyncSessionLocal
from module_sampling.service.sample_record_service import SampleRecordService
from module_sampling.dao.sampling_task_group_dao import SamplingTaskGroupDAO
from utils.log_util import logger

async def test_sample_record_generation():
    """测试样品记录生成"""
    async with AsyncSessionLocal() as db:
        try:
            # 查找最新的任务分组
            group_dao = SamplingTaskGroupDAO(db)
            
            # 查询最新的分组
            from sqlalchemy import select, desc
            from module_sampling.entity.do.sampling_task_group_do import SamplingTaskGroup
            
            stmt = select(SamplingTaskGroup).order_by(desc(SamplingTaskGroup.id)).limit(1)
            result = await db.execute(stmt)
            latest_group = result.scalars().first()
            
            if not latest_group:
                print("❌ 没有找到任务分组")
                return
                
            print(f"✅ 找到最新分组: ID={latest_group.id}, 分组编号={latest_group.group_code}")
            
            # 测试样品记录生成
            sample_service = SampleRecordService(db)
            
            print(f"🔄 开始为分组 {latest_group.group_code} 生成样品记录...")
            
            try:
                records = await sample_service.generate_sample_records_for_group(latest_group.id, 1)
                print(f"✅ 样品记录生成成功，共生成 {len(records)} 条记录")
                
                for record in records:
                    print(f"  - 样品编号: {record.sample_number}")
                    
            except Exception as e:
                print(f"❌ 样品记录生成失败: {str(e)}")
                print(f"详细错误信息:")
                traceback.print_exc()
                
                # 尝试手动调试每个步骤
                print("\n🔍 开始详细调试...")
                
                # 1. 检查分组详情
                try:
                    group = await sample_service._get_group_with_details(latest_group.id)
                    if group:
                        print(f"✅ 分组详情获取成功: {group.group_code}")
                        print(f"  - 任务ID: {group.sampling_task_id}")
                        print(f"  - 任务编号: {group.sampling_task.task_code if group.sampling_task else 'None'}")
                    else:
                        print("❌ 分组详情获取失败")
                        return
                except Exception as e:
                    print(f"❌ 获取分组详情失败: {str(e)}")
                    traceback.print_exc()
                    return
                
                # 2. 检查周期条目
                try:
                    cycle_items = await sample_service._get_cycle_items_for_group(group)
                    print(f"✅ 周期条目获取成功，共 {len(cycle_items)} 个")
                    for item in cycle_items:
                        print(f"  - 周期条目ID: {item.id}, 类别: {item.project_quotation_item_point_item.category if item.project_quotation_item_point_item else 'None'}")
                except Exception as e:
                    print(f"❌ 获取周期条目失败: {str(e)}")
                    traceback.print_exc()
                    return
                
                # 3. 检查类别查询
                if cycle_items:
                    try:
                        first_item = cycle_items[0]
                        if first_item.project_quotation_item_point_item:
                            category = first_item.project_quotation_item_point_item.category
                            print(f"🔍 测试类别查询: {category}")
                            
                            category_obj = await sample_service.category_dao.get_by_category(category)
                            if category_obj:
                                print(f"✅ 类别查询成功: {category_obj.classification}")
                            else:
                                print(f"⚠️ 类别查询返回空: {category}")
                    except Exception as e:
                        print(f"❌ 类别查询失败: {str(e)}")
                        traceback.print_exc()
                
        except Exception as e:
            print(f"❌ 测试过程中发生异常: {str(e)}")
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_sample_record_generation())
