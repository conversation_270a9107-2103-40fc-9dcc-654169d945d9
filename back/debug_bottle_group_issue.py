#!/usr/bin/env python3
"""
调试瓶组编号2510007G1S001B004显示为默认瓶组的问题
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from config.get_db import get_db
from module_sampling.entity.do.sampling_bottle_group_do import SamplingBottleGroup
from module_bottle_maintenance.entity.do.bottle_maintenance_do import BottleMaintenance
from module_bottle_maintenance.entity.do.bottle_maintenance_technical_manual_do import BottleMaintenanceTechnicalManual
from module_basedata.entity.do.technical_manual_do import TechnicalManual
from module_sampling.entity.do.sample_record_do import SampleRecord
from module_sampling.entity.do.sampling_bottle_group_sample_do import SamplingBottleGroupSample


async def debug_bottle_group_issue():
    """调试瓶组问题"""
    print("=" * 80)
    print("调试瓶组编号显示为默认瓶组的问题")
    print("=" * 80)
    
    async for db in get_db():
        try:
            # 1. 查询瓶组编号2510007G1S001B004和2510007G1S001B001的记录
            print("\n1. 查询瓶组记录...")
            bottle_codes = ['2510007G1S001B004', '2510007G1S001B001']
            
            stmt = select(SamplingBottleGroup).where(
                SamplingBottleGroup.bottle_group_code.in_(bottle_codes)
            ).order_by(SamplingBottleGroup.bottle_group_code)
            
            result = await db.execute(stmt)
            bottle_groups = result.scalars().all()
            
            if not bottle_groups:
                print("❌ 未找到指定的瓶组记录")
                return
            
            print(f"✅ 找到 {len(bottle_groups)} 个瓶组记录:")
            for bg in bottle_groups:
                print(f"  瓶组编号: {bg.bottle_group_code}")
                print(f"  瓶组ID: {bg.id}")
                print(f"  任务ID: {bg.sampling_task_id}")
                print(f"  瓶组管理ID: {bg.bottle_maintenance_id} {'(默认瓶组)' if bg.bottle_maintenance_id is None else '(匹配瓶组)'}")
                print(f"  检测方法: {bg.detection_method}")
                print(f"  样品数量: {bg.sample_count}")
                print("-" * 40)
            
            # 2. 查询这些瓶组关联的样品记录
            print("\n2. 查询瓶组关联的样品记录...")
            bottle_group_ids = [bg.id for bg in bottle_groups]
            
            stmt = select(
                SamplingBottleGroupSample.bottle_group_id,
                SampleRecord.id,
                SampleRecord.sample_number,
                SampleRecord.detection_category,
                SampleRecord.detection_parameter,
                SampleRecord.detection_method
            ).join(
                SampleRecord, SamplingBottleGroupSample.sample_record_id == SampleRecord.id
            ).where(
                SamplingBottleGroupSample.bottle_group_id.in_(bottle_group_ids)
            ).order_by(SamplingBottleGroupSample.bottle_group_id)
            
            result = await db.execute(stmt)
            sample_relations = result.fetchall()
            
            print(f"✅ 找到 {len(sample_relations)} 个样品关联记录:")
            for relation in sample_relations:
                bottle_group = next((bg for bg in bottle_groups if bg.id == relation.bottle_group_id), None)
                print(f"  瓶组: {bottle_group.bottle_group_code if bottle_group else 'Unknown'}")
                print(f"    样品ID: {relation.id}")
                print(f"    样品编号: {relation.sample_number}")
                print(f"    检测类别: {relation.detection_category}")
                print(f"    检测参数: {relation.detection_parameter}")
                print(f"    检测方法: {relation.detection_method}")
                print("-" * 40)
            
            # 3. 检查技术手册匹配情况
            print("\n3. 检查技术手册匹配情况...")
            
            # 获取样品的检测三元组信息
            unique_triples = set()
            for relation in sample_relations:
                triple = (
                    relation.detection_category or "",
                    relation.detection_parameter or "",
                    relation.detection_method or ""
                )
                unique_triples.add(triple)
            
            print(f"✅ 发现 {len(unique_triples)} 个不同的检测三元组:")
            for i, (category, parameter, method) in enumerate(unique_triples, 1):
                print(f"\n  三元组 {i}:")
                print(f"    检测类别: '{category}'")
                print(f"    检测参数: '{parameter}'")
                print(f"    检测方法: '{method}'")
                
                # 查找匹配的技术手册
                conditions = [TechnicalManual.status == "0"]
                if parameter:
                    conditions.append(TechnicalManual.parameter == parameter)
                if method:
                    conditions.append(TechnicalManual.method == method)
                
                stmt = select(TechnicalManual).where(and_(*conditions))
                result = await db.execute(stmt)
                manuals = result.scalars().all()
                
                print(f"    匹配的技术手册数量: {len(manuals)}")
                
                if manuals:
                    for manual in manuals:
                        print(f"      技术手册ID: {manual.id}")
                        print(f"      参数: {manual.parameter}")
                        print(f"      方法: {manual.method}")
                        print(f"      类目编码: {manual.category_codes}")
                        
                        # 查找关联的瓶组管理
                        stmt = select(BottleMaintenance).join(
                            BottleMaintenanceTechnicalManual,
                            BottleMaintenance.id == BottleMaintenanceTechnicalManual.bottle_maintenance_id
                        ).where(
                            BottleMaintenanceTechnicalManual.technical_manual_id == manual.id
                        )
                        result = await db.execute(stmt)
                        bottle_maintenances = result.scalars().all()
                        
                        print(f"      关联的瓶组管理数量: {len(bottle_maintenances)}")
                        for bm in bottle_maintenances:
                            print(f"        瓶组管理ID: {bm.id}")
                            print(f"        瓶组编码: {bm.bottle_code}")
                            print(f"        容器类型: {bm.bottle_type}")
                else:
                    print("    ❌ 未找到匹配的技术手册")
                
                print("-" * 60)
            
            # 4. 分析问题原因
            print("\n4. 问题分析:")
            print("=" * 60)
            
            # 检查是否有相同样品的不同瓶组有不同的bottle_maintenance_id
            sample_bottle_map = {}
            for relation in sample_relations:
                sample_number = relation.sample_number
                bottle_group = next((bg for bg in bottle_groups if bg.id == relation.bottle_group_id), None)
                if bottle_group:
                    if sample_number not in sample_bottle_map:
                        sample_bottle_map[sample_number] = []
                    sample_bottle_map[sample_number].append({
                        'bottle_code': bottle_group.bottle_group_code,
                        'bottle_maintenance_id': bottle_group.bottle_maintenance_id,
                        'detection_method': bottle_group.detection_method,
                        'detection_category': relation.detection_category,
                        'detection_parameter': relation.detection_parameter
                    })
            
            for sample_number, bottles in sample_bottle_map.items():
                print(f"\n样品 {sample_number} 的瓶组情况:")
                for bottle in bottles:
                    status = "默认瓶组" if bottle['bottle_maintenance_id'] is None else f"匹配瓶组(ID:{bottle['bottle_maintenance_id']})"
                    print(f"  {bottle['bottle_code']}: {status}")
                    print(f"    检测方法: {bottle['detection_method']}")
                    print(f"    检测类别: {bottle['detection_category']}")
                    print(f"    检测参数: {bottle['detection_parameter']}")
                
                # 检查是否应该使用相同的瓶组
                maintenance_ids = [b['bottle_maintenance_id'] for b in bottles]
                if len(set(maintenance_ids)) > 1:
                    print(f"  ⚠️  警告: 同一样品的不同瓶组使用了不同的瓶组管理配置!")
                    print(f"    瓶组管理ID列表: {maintenance_ids}")
            
        except Exception as e:
            print(f"❌ 调试过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()
        
        finally:
            await db.close()


if __name__ == "__main__":
    asyncio.run(debug_bottle_group_issue())
