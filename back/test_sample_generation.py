#!/usr/bin/env python3
"""
测试样品记录生成功能
"""
import asyncio
import sys
import traceback
sys.path.append('.')

from config.database import AsyncSessionLocal
from module_sampling.service.sample_record_service import SampleRecordService

async def test_sample_generation():
    """测试样品记录生成"""
    async with AsyncSessionLocal() as db:
        try:
            service = SampleRecordService(db)
            
            # 测试为分组134生成样品记录
            group_id = 134
            create_by = 1
            
            print(f"开始为分组 {group_id} 生成样品记录...")
            
            result = await service.generate_sample_records_for_group(group_id, create_by)
            
            print(f"✅ 成功生成 {len(result)} 条样品记录")
            for record in result:
                print(f"  - 样品编号: {record.sample_number}")
                
        except Exception as e:
            print(f"❌ 样品记录生成失败: {str(e)}")
            print(f"错误类型: {type(e).__name__}")
            print("详细错误信息:")
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_sample_generation())
