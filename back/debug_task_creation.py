#!/usr/bin/env python3
"""
调试采样任务创建问题的脚本
"""
import asyncio
import sys
import traceback
from datetime import datetime

# 添加项目路径
sys.path.append('.')

from config.database import AsyncSessionLocal
from module_sampling.service.sampling_task_creation_service import SamplingTaskCreationService
from module_sampling.entity.vo.sampling_task_creation_vo import SamplingTaskCreationModel
from module_sampling.dao.sampling_task_dao import Sampling<PERSON>askDao
from module_sampling.dao.sample_record_dao import SampleRecordDAO
from module_sampling.dao.sampling_task_group_dao import SamplingTaskGroupDAO

async def debug_task_creation():
    """调试任务创建过程"""
    print("🔍 开始调试采样任务创建问题...")
    
    async with AsyncSessionLocal() as db:
        try:
            # 1. 检查现有任务数量
            task_dao = SamplingTaskDao(db)
            existing_tasks_result = await task_dao.page_sampling_tasks(
                page=1, size=100,
                task_name=None, status=None
            )
            existing_tasks = {
                'total': existing_tasks_result[1],
                'records': [task.__dict__ for task in existing_tasks_result[0]]
            }
            print(f"📊 现有任务数量: {existing_tasks['total']}")
            
            # 2. 创建一个简单的测试任务
            creation_service = SamplingTaskCreationService(db)
            
            # 使用最少的数据进行测试
            test_data = SamplingTaskCreationModel(
                projectQuotationId=128,
                taskName="DEBUG-测试任务",
                taskDescription="调试用测试任务",
                selectedCycleItemIds=[95],  # 只选择一个周期条目
                responsibleUserId=101,
                memberUserIds=[101],
                plannedStartTime=None,
                plannedEndTime=None
            )
            
            print(f"🚀 开始创建测试任务...")
            print(f"   项目报价ID: {test_data.project_quotation_id}")
            print(f"   周期条目数量: {len(test_data.selected_cycle_item_ids)}")
            
            # 3. 执行任务创建
            result = await creation_service.create_sampling_task(test_data, user_id=101)
            
            print(f"✅ 任务创建API返回结果:")
            print(f"   成功: {result.success}")
            print(f"   任务ID: {result.task_id}")
            print(f"   任务编号: {result.task_number}")
            print(f"   消息: {result.message}")
            
            # 4. 验证任务是否真的存在于数据库中
            if result.task_id:
                print(f"\n🔍 验证任务ID {result.task_id} 是否存在...")
                
                # 直接查询任务表
                task = await task_dao.get_sampling_task_by_id(result.task_id)
                if task:
                    print(f"✅ 任务记录存在:")
                    print(f"   ID: {task.id}")
                    print(f"   编号: {task.task_number}")
                    print(f"   名称: {task.task_name}")
                    print(f"   状态: {task.status}")
                    print(f"   项目报价ID: {task.project_quotation_id}")
                else:
                    print(f"❌ 任务记录不存在!")
                
                # 检查任务分组
                group_dao = SamplingTaskGroupDAO(db)
                groups = await group_dao.get_groups_by_task_id(result.task_id)
                print(f"\n📦 任务分组数量: {len(groups)}")
                for group in groups:
                    print(f"   分组: {group.group_number} - {group.group_name}")
                
                # 检查样品记录
                sample_dao = SampleRecordDAO(db)
                samples = await sample_dao.get_samples_by_task_id(result.task_id)
                print(f"\n🧪 样品记录数量: {len(samples)}")
                for sample in samples[:5]:  # 只显示前5个
                    print(f"   样品: {sample.sample_number} - {sample.sample_name}")
            
            # 5. 检查任务列表查询
            print(f"\n📋 检查任务列表查询...")
            updated_tasks_result = await task_dao.page_sampling_tasks(
                page=1, size=100,
                task_name=None, status=None
            )
            updated_tasks = {
                'total': updated_tasks_result[1],
                'records': [task.__dict__ for task in updated_tasks_result[0]]
            }
            print(f"   更新后任务数量: {updated_tasks['total']}")
            
            # 查找我们刚创建的任务
            found_task = None
            for task_data in updated_tasks['records']:
                if task_data.get('id') == result.task_id:
                    found_task = task_data
                    break
            
            if found_task:
                print(f"✅ 在任务列表中找到新创建的任务:")
                print(f"   ID: {found_task.get('id')}")
                print(f"   编号: {found_task.get('task_number')}")
                print(f"   名称: {found_task.get('task_name')}")
            else:
                print(f"❌ 在任务列表中未找到新创建的任务!")
                print(f"   可能的原因:")
                print(f"   1. 任务列表查询的JOIN条件有问题")
                print(f"   2. 任务状态过滤有问题")
                print(f"   3. 数据库事务问题")
                
                # 显示最近的几个任务
                print(f"\n📝 最近的任务列表:")
                for i, task_data in enumerate(updated_tasks['records'][:5]):
                    print(f"   {i+1}. ID:{task_data.get('id')} 编号:{task_data.get('task_number')} 名称:{task_data.get('task_name')}")
            
        except Exception as e:
            print(f"❌ 调试过程中发生错误: {str(e)}")
            print(f"错误详情:")
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_task_creation())
