"""
模型导入文件
确保核心 SQLAlchemy 模型都被正确导入和注册
"""

# 客户模块模型
from module_customer.entity.do.customer_do import Customer, CustomerContact, CustomerInternalManager
# from module_customer.entity.do.customer_follow_record_do import CustomerFollowRecord  # 暂时注释掉以解决 SQLAlchemy 映射问题

# 报价模块模型
from module_quotation.entity.do.project_quotation_do import ProjectQuotation
from module_quotation.entity.do.project_quotation_item_point_item_do import ProjectQuotationItemPointItem

# 采样模块模型
from module_sampling.entity.do.sampling_task_do import SamplingTask
from module_sampling.entity.do.sampling_task_group_do import SamplingTaskGroup
from module_sampling.entity.do.sampling_task_member_do import SamplingTaskMember
from module_sampling.entity.do.sampling_task_cycle_item_do import SamplingTaskCycleItem
from module_sampling.entity.do.sample_record_do import SampleRecord
from module_sampling.entity.do.sampling_bottle_group_do import SamplingBottleGroup
from module_sampling.entity.do.sampling_point_info_do import SamplingPointInfo
from module_sampling.entity.do.sampling_task_sequence_do import SamplingTaskSequence
from module_sampling.entity.do.sample_record_sequence_do import SampleRecordSequence
from module_sampling.entity.do.sampling_bottle_group_sequence_do import SamplingBottleGroupSequence
from module_sampling.entity.do.detection_cycle_item_do import DetectionCycleItem
from module_sampling.entity.do.sample_box_do import SampleBox
from module_sampling.entity.do.sample_box_sample_do import SampleBoxSample
from module_sampling.entity.do.sample_box_sequence_do import SampleBoxSequence

__all__ = [
    # 客户模块
    'Customer', 'CustomerContact', 'CustomerInternalManager',  # 'CustomerFollowRecord',  # 暂时注释掉以解决 SQLAlchemy 映射问题

    # 报价模块
    'ProjectQuotation', 'ProjectQuotationItemPointItem', 'DetectionCycleItem',

    # 采样模块
    'SamplingTask', 'SamplingTaskGroup', 'SamplingTaskMember', 'SamplingTaskCycleItem',
    'SampleRecord', 'SamplingBottleGroup', 'SamplingPointInfo',
    'SamplingTaskSequence', 'SampleRecordSequence', 'SamplingBottleGroupSequence',
    'SampleBox', 'SampleBoxSample', 'SampleBoxSequence',
]
