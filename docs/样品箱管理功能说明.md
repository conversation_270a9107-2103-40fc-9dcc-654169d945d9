# 样品箱管理功能说明

## 概述

样品箱管理功能是LIMS2系统采样管理模块的重要组成部分，主要负责管理实验室样品的装箱、封箱和发送流程。该功能支持样品的分类装箱、状态跟踪和编号管理。

## 功能特性

### 1. 样品箱管理

- **新建样品箱**：创建新的样品箱，自动生成包含日期的唯一编号
- **查看详情**：查看样品箱的详细信息，包括包含的样品列表
- **修改样品箱**：修改样品箱的基本信息和包含的样品（仅限未封箱状态）
- **删除样品箱**：删除未封箱的样品箱
- **封箱操作**：对样品箱进行封箱，封箱后不可修改
- **状态跟踪**：跟踪样品箱的状态变化（新建→已封箱→已发送）

### 2. 编号规则

样品箱编号格式：**XP + YYYYMMDD + 序号**

- **XP**：样品箱前缀标识
- **YYYYMMDD**：创建日期（8位）
- **序号**：当日创建的序号（3位，从001开始）

示例：`XP20241104001`（2024年11月4日创建的第1个样品箱）

### 3. 状态管理

- **0 - 新建**：刚创建的样品箱，可以修改和删除
- **1 - 已封箱**：已经封箱的样品箱，不可修改，可以发送
- **2 - 已发送**：已经发送的样品箱，完成流程

### 4. 样品关联

- 支持将多个样品记录添加到样品箱中
- 支持从样品箱中移除样品记录
- 自动统计样品箱中的样品数量
- 空样品箱不允许封箱

## 数据库设计

### 主要数据表

1. **sample_box** - 样品箱表
   - 存储样品箱基本信息
   - 包含编号、名称、状态、封箱信息等

2. **sample_box_sample** - 样品箱样品关联表
   - 建立样品箱与样品记录的多对多关系

3. **sample_box_sequence** - 样品箱编号序列表
   - 用于生成唯一的样品箱编号

### 表结构详情

```sql
-- 样品箱表
CREATE TABLE `sample_box` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sampling_task_id` bigint NOT NULL COMMENT '采样任务ID',
  `box_code` varchar(100) NOT NULL COMMENT '样品箱编号',
  `box_name` varchar(200) DEFAULT NULL COMMENT '样品箱名称',
  `description` text COMMENT '样品箱描述',
  `sample_count` int DEFAULT '0' COMMENT '样品数量',
  `status` int DEFAULT '0' COMMENT '状态：0-新建，1-已封箱，2-已发送',
  `is_sealed` tinyint(1) DEFAULT '0' COMMENT '是否已封箱',
  `sealed_time` datetime DEFAULT NULL COMMENT '封箱时间',
  `sealed_by` bigint DEFAULT NULL COMMENT '封箱人',
  `create_by` bigint DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_box_code` (`box_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='样品箱表';
```

## API接口

### 后端接口

- `POST /sampling/sample-box/create` - 创建样品箱
- `GET /sampling/sample-box/{id}` - 获取样品箱详情
- `PUT /sampling/sample-box/{id}` - 更新样品箱
- `DELETE /sampling/sample-box/{id}` - 删除样品箱
- `PUT /sampling/sample-box/{id}/seal` - 封箱操作
- `GET /sampling/sample-box/task/{taskId}` - 获取任务的样品箱列表
- `GET /sampling/sample-box/list` - 分页查询样品箱列表
- `GET /sampling/sample-box/statistics/task/{taskId}` - 获取任务统计信息
- `GET /sampling/sample-box/statistics/all` - 获取全部统计信息

### 前端API

- `listSampleBox(query)` - 查询样品箱列表
- `getSampleBox(boxId)` - 查询样品箱详情
- `addSampleBox(data)` - 新增样品箱
- `updateSampleBox(boxId, data)` - 修改样品箱
- `delSampleBox(boxId)` - 删除样品箱
- `sealSampleBox(boxId)` - 封箱操作
- `getSampleBoxesByTask(taskId)` - 根据任务ID获取样品箱列表
- `getSampleBoxStatisticsByTask(taskId)` - 获取任务统计信息
- `getAllSampleBoxStatistics()` - 获取全部统计信息

## 文件结构

### 后端文件

```
back/
├── module_sampling/
│   ├── controller/
│   │   └── sample_box_controller.py          # 样品箱控制器
│   ├── service/
│   │   └── sample_box_service.py             # 样品箱服务
│   ├── dao/
│   │   ├── sample_box_dao.py                 # 样品箱数据访问层
│   │   └── sample_box_sample_dao.py          # 样品箱样品关联数据访问层
│   ├── dto/
│   │   └── sample_box_dto.py                 # 样品箱数据传输对象
│   └── entity/do/
│       ├── sample_box_do.py                  # 样品箱数据模型
│       ├── sample_box_sample_do.py           # 样品箱样品关联模型
│       └── sample_box_sequence_do.py         # 样品箱序列模型
├── migrations/
│   └── create_sample_box_tables.sql          # 数据库迁移脚本
└── tests/
    └── test_sample_box.py                    # 单元测试
```

### 前端文件

```
front/
├── src/
│   ├── api/sampling/
│   │   └── sampleBox.js                      # 样品箱API
│   ├── views/sampling/
│   │   └── sampleBox/
│   │       └── index.vue                     # 样品箱管理页面
│   └── router/modules/
│       └── sampling.js                       # 采样模块路由配置（已更新）
```

## 部署说明

### 1. 数据库初始化

```bash
# 执行数据库迁移脚本
mysql -u username -p database_name < back/migrations/create_sample_box_tables.sql
```

### 2. 后端部署

1. 确保样品箱控制器已在`server.py`中注册
2. 确保样品箱模型已在`models/__init__.py`中导入
3. 重启后端服务

### 3. 前端部署

1. 确保前端页面文件已正确放置
2. 路由配置已正确设置
3. 重新构建前端项目

## 使用流程

### 1. 创建样品箱

1. 在采样管理菜单中点击"样品箱管理"
2. 点击"新增"按钮
3. 填写样品箱信息（任务ID、名称、描述）
4. 选择要添加的样品记录
5. 点击"确定"创建样品箱

### 2. 管理样品箱

1. 在样品箱列表中查看所有样品箱
2. 点击"详情"查看样品箱详细信息
3. 点击"修改"编辑样品箱信息（仅限未封箱）
4. 点击"封箱"对样品箱进行封箱操作
5. 点击"删除"删除未封箱的样品箱

### 3. 样品箱状态流转

```
新建 → 已封箱 → 已发送
```

- **新建**：可以修改、删除、封箱
- **已封箱**：只能查看详情，可以标记为已发送
- **已发送**：只能查看详情，流程结束

## 权限控制

- `sampling:sampleBox:add` - 新增样品箱权限
- `sampling:sampleBox:edit` - 修改样品箱权限
- `sampling:sampleBox:remove` - 删除样品箱权限
- `sampling:sampleBox:query` - 查询样品箱权限
- `sampling:sampleBox:seal` - 封箱操作权限

## 注意事项

1. 样品箱编号自动生成，不可手动修改
2. 封箱后的样品箱不能修改或删除
3. 空样品箱不允许封箱
4. 删除样品箱会同时删除相关的样品关联记录
5. 样品箱与采样任务关联，删除任务时需要考虑相关样品箱的处理
