/**
 * 蓝牙连接测试工具
 * 用于测试和验证蓝牙连接功能
 */

const CPCLPrintAdapter = require('./adapter.js');
const BluetoothDiagnostic = require('./bluetooth-diagnostic.js');
const ConnectionManager = require('./connection-manager.js');

class BluetoothConnectionTest {
  constructor() {
    this.adapter = new CPCLPrintAdapter();
    this.diagnostic = new BluetoothDiagnostic();
    this.connectionManager = new ConnectionManager();
    this.testResults = [];
  }

  /**
   * 运行完整测试套件
   */
  async runFullTest() {
    console.log('开始蓝牙连接测试...');
    this.testResults = [];

    try {
      // 1. 系统诊断测试
      await this.testSystemDiagnostic();

      // 2. 适配器初始化测试
      await this.testAdapterInitialization();

      // 3. 设备搜索测试
      await this.testDeviceSearch();

      // 4. 连接管理器测试
      await this.testConnectionManager();

      // 5. 生成测试报告
      const report = this.generateTestReport();
      console.log('测试完成，报告:', report);
      
      return report;

    } catch (error) {
      console.error('测试过程中出现错误:', error);
      this.addTestResult('测试执行', 'error', `测试失败: ${error.message}`);
      return this.generateTestReport();
    }
  }

  /**
   * 测试系统诊断
   */
  async testSystemDiagnostic() {
    console.log('测试系统诊断...');
    
    try {
      const results = await this.diagnostic.runFullDiagnostic();
      const hasErrors = results.some(r => r.type === 'error');
      
      if (hasErrors) {
        this.addTestResult('系统诊断', 'warning', '发现系统问题', { results });
      } else {
        this.addTestResult('系统诊断', 'success', '系统状态正常', { results });
      }
      
    } catch (error) {
      this.addTestResult('系统诊断', 'error', `诊断失败: ${error.message}`);
    }
  }

  /**
   * 测试适配器初始化
   */
  async testAdapterInitialization() {
    console.log('测试适配器初始化...');
    
    try {
      await this.adapter.initBluetooth();
      this.addTestResult('适配器初始化', 'success', '初始化成功');
      
    } catch (error) {
      this.addTestResult('适配器初始化', 'error', `初始化失败: ${error.message}`);
    }
  }

  /**
   * 测试设备搜索
   */
  async testDeviceSearch() {
    console.log('测试设备搜索...');
    
    try {
      const devices = await this.adapter.searchDevices();
      
      if (devices.length > 0) {
        this.addTestResult('设备搜索', 'success', `发现 ${devices.length} 个设备`, { devices });
      } else {
        this.addTestResult('设备搜索', 'warning', '未发现设备');
      }
      
    } catch (error) {
      this.addTestResult('设备搜索', 'error', `搜索失败: ${error.message}`);
    }
  }

  /**
   * 测试连接管理器
   */
  async testConnectionManager() {
    console.log('测试连接管理器...');
    
    try {
      // 测试连接管理器的基本功能
      const stats = this.connectionManager.getConnectionStats();
      this.addTestResult('连接管理器', 'success', '管理器工作正常', { stats });
      
    } catch (error) {
      this.addTestResult('连接管理器', 'error', `管理器测试失败: ${error.message}`);
    }
  }

  /**
   * 测试特定设备连接
   */
  async testDeviceConnection(deviceId) {
    console.log(`测试设备连接: ${deviceId}`);
    
    try {
      // 使用智能连接
      const result = await this.adapter.smartConnectDevice(deviceId, {
        maxRetries: 2,
        enableDiagnostic: false
      });
      
      this.addTestResult('设备连接', 'success', `连接成功: ${deviceId}`, { result });
      
      // 测试断开连接
      await this.adapter.closeBLEConnection(deviceId);
      this.addTestResult('设备断开', 'success', `断开成功: ${deviceId}`);
      
    } catch (error) {
      this.addTestResult('设备连接', 'error', `连接失败: ${error.message}`, {
        deviceId,
        suggestions: error.suggestions
      });
    }
  }

  /**
   * 模拟10002错误测试
   */
  async testError10002Handling() {
    console.log('测试10002错误处理...');
    
    try {
      // 创建模拟的10002错误
      const mockError = new Error('连接失败：设备不可连接或已被其他应用占用');
      mockError.errCode = 10002;
      
      // 测试错误增强
      const enhancedError = this.connectionManager.enhanceError(mockError, 'test-device');
      
      if (enhancedError.suggestions && enhancedError.suggestions.length > 0) {
        this.addTestResult('错误处理', 'success', '10002错误处理正常', {
          suggestions: enhancedError.suggestions
        });
      } else {
        this.addTestResult('错误处理', 'warning', '错误处理缺少建议');
      }
      
    } catch (error) {
      this.addTestResult('错误处理', 'error', `错误处理测试失败: ${error.message}`);
    }
  }

  /**
   * 性能测试
   */
  async testPerformance() {
    console.log('测试性能...');
    
    const startTime = Date.now();
    
    try {
      // 测试初始化性能
      const initStart = Date.now();
      await this.adapter.initBluetooth();
      const initTime = Date.now() - initStart;
      
      // 测试搜索性能
      const searchStart = Date.now();
      await this.adapter.searchDevices();
      const searchTime = Date.now() - searchStart;
      
      const totalTime = Date.now() - startTime;
      
      this.addTestResult('性能测试', 'success', '性能测试完成', {
        initTime: `${initTime}ms`,
        searchTime: `${searchTime}ms`,
        totalTime: `${totalTime}ms`
      });
      
    } catch (error) {
      this.addTestResult('性能测试', 'error', `性能测试失败: ${error.message}`);
    }
  }

  /**
   * 添加测试结果
   */
  addTestResult(category, type, message, details = {}) {
    this.testResults.push({
      category,
      type,
      message,
      details,
      timestamp: new Date().toISOString()
    });
    
    console.log(`[${category}] ${type.toUpperCase()}: ${message}`, details);
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    const summary = {
      total: this.testResults.length,
      success: this.testResults.filter(r => r.type === 'success').length,
      warning: this.testResults.filter(r => r.type === 'warning').length,
      error: this.testResults.filter(r => r.type === 'error').length
    };
    
    const report = {
      summary,
      results: this.testResults,
      timestamp: new Date().toISOString(),
      passed: summary.error === 0
    };
    
    return report;
  }

  /**
   * 导出测试报告为文本
   */
  exportReportText() {
    const report = this.generateTestReport();
    
    let text = '=== 蓝牙连接测试报告 ===\n';
    text += `生成时间: ${report.timestamp}\n`;
    text += `测试状态: ${report.passed ? '通过' : '失败'}\n\n`;
    
    text += '=== 摘要 ===\n';
    text += `总测试项: ${report.summary.total}\n`;
    text += `成功: ${report.summary.success}\n`;
    text += `警告: ${report.summary.warning}\n`;
    text += `错误: ${report.summary.error}\n\n`;
    
    text += '=== 详细结果 ===\n';
    report.results.forEach((result, index) => {
      text += `${index + 1}. [${result.category}] ${result.type.toUpperCase()}: ${result.message}\n`;
      if (Object.keys(result.details).length > 0) {
        text += `   详情: ${JSON.stringify(result.details, null, 2)}\n`;
      }
      text += '\n';
    });
    
    return text;
  }

  /**
   * 清理测试环境
   */
  async cleanup() {
    try {
      // 关闭蓝牙适配器
      wx.closeBluetoothAdapter({
        complete: () => {
          console.log('测试环境清理完成');
        }
      });
    } catch (error) {
      console.log('清理测试环境失败:', error);
    }
  }
}

// 导出测试类
module.exports = BluetoothConnectionTest;

// 如果直接运行，执行测试
if (typeof module !== 'undefined' && require.main === module) {
  const test = new BluetoothConnectionTest();
  test.runFullTest().then(report => {
    console.log('测试报告:', report);
    console.log('文本报告:\n', test.exportReportText());
  });
}
