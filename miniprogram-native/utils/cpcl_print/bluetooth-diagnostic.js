/**
 * 蓝牙连接诊断工具
 * 用于诊断和解决蓝牙连接问题
 */

class BluetoothDiagnostic {
  constructor() {
    this.diagnosticResults = [];
  }

  /**
   * 执行完整的蓝牙诊断
   */
  async runFullDiagnostic() {
    this.diagnosticResults = [];
    
    console.log('开始蓝牙诊断...');
    
    // 1. 检查微信版本
    await this.checkWechatVersion();
    
    // 2. 检查蓝牙适配器状态
    await this.checkBluetoothAdapter();
    
    // 3. 检查系统蓝牙状态
    await this.checkSystemBluetooth();
    
    // 4. 检查权限
    await this.checkPermissions();
    
    // 5. 检查设备兼容性
    await this.checkDeviceCompatibility();
    
    return this.diagnosticResults;
  }

  /**
   * 检查微信版本
   */
  async checkWechatVersion() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      const version = systemInfo.version;
      
      this.addResult('微信版本检查', 'success', `当前版本: ${version}`, {
        version,
        platform: systemInfo.platform,
        system: systemInfo.system
      });
      
      // 检查是否支持蓝牙API
      if (!wx.openBluetoothAdapter) {
        this.addResult('蓝牙API支持', 'error', '当前微信版本不支持蓝牙API，请升级微信', {
          recommendation: '请升级到最新版本微信'
        });
      } else {
        this.addResult('蓝牙API支持', 'success', '支持蓝牙API');
      }
      
    } catch (error) {
      this.addResult('微信版本检查', 'error', `检查失败: ${error.message}`);
    }
  }

  /**
   * 检查蓝牙适配器状态
   */
  async checkBluetoothAdapter() {
    try {
      await new Promise((resolve, reject) => {
        wx.openBluetoothAdapter({
          success: (res) => {
            this.addResult('蓝牙适配器初始化', 'success', '初始化成功');
            resolve(res);
          },
          fail: (err) => {
            let message = '初始化失败';
            let recommendation = '';
            
            switch (err.errCode) {
              case 10001:
                message = '未找到蓝牙适配器';
                recommendation = '请检查设备是否支持蓝牙功能';
                break;
              case 10000:
                message = '未授权使用蓝牙';
                recommendation = '请在设置中开启小程序蓝牙权限';
                break;
              default:
                message = `初始化失败: ${err.errMsg}`;
                recommendation = '请确保已开启手机蓝牙功能';
            }
            
            this.addResult('蓝牙适配器初始化', 'error', message, {
              errCode: err.errCode,
              recommendation
            });
            reject(err);
          }
        });
      });
      
      // 获取蓝牙适配器状态
      await new Promise((resolve) => {
        wx.getBluetoothAdapterState({
          success: (res) => {
            const status = res.available ? '可用' : '不可用';
            const discovering = res.discovering ? '搜索中' : '未搜索';
            
            this.addResult('蓝牙适配器状态', res.available ? 'success' : 'warning', 
              `状态: ${status}, ${discovering}`, {
                available: res.available,
                discovering: res.discovering
              });
            resolve(res);
          },
          fail: (err) => {
            this.addResult('蓝牙适配器状态', 'error', `获取状态失败: ${err.errMsg}`);
            resolve();
          }
        });
      });
      
    } catch (error) {
      // 已在上面处理
    }
  }

  /**
   * 检查系统蓝牙状态
   */
  async checkSystemBluetooth() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      
      this.addResult('系统信息', 'info', 
        `平台: ${systemInfo.platform}, 系统: ${systemInfo.system}`, {
          platform: systemInfo.platform,
          system: systemInfo.system,
          model: systemInfo.model,
          brand: systemInfo.brand
        });
      
      // 检查已连接的蓝牙设备
      wx.getConnectedBluetoothDevices({
        services: [],
        success: (res) => {
          const count = res.devices.length;
          this.addResult('已连接设备', 'info', `已连接 ${count} 个蓝牙设备`, {
            devices: res.devices
          });
        },
        fail: (err) => {
          this.addResult('已连接设备', 'warning', `无法获取已连接设备: ${err.errMsg}`);
        }
      });
      
    } catch (error) {
      this.addResult('系统蓝牙状态', 'error', `检查失败: ${error.message}`);
    }
  }

  /**
   * 检查权限
   */
  async checkPermissions() {
    try {
      // 检查位置权限（Android需要）
      wx.getSetting({
        success: (res) => {
          const locationAuth = res.authSetting['scope.userLocation'];
          if (locationAuth === false) {
            this.addResult('位置权限', 'warning', '位置权限被拒绝，可能影响蓝牙搜索', {
              recommendation: 'Android设备搜索蓝牙需要位置权限，请在设置中开启'
            });
          } else if (locationAuth === true) {
            this.addResult('位置权限', 'success', '位置权限已授权');
          } else {
            this.addResult('位置权限', 'info', '位置权限未请求');
          }
        },
        fail: (err) => {
          this.addResult('权限检查', 'error', `检查失败: ${err.errMsg}`);
        }
      });
      
    } catch (error) {
      this.addResult('权限检查', 'error', `检查失败: ${error.message}`);
    }
  }

  /**
   * 检查设备兼容性
   */
  async checkDeviceCompatibility() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      
      // 检查已知的兼容性问题
      const knownIssues = this.getKnownCompatibilityIssues(systemInfo);
      
      if (knownIssues.length > 0) {
        knownIssues.forEach(issue => {
          this.addResult('设备兼容性', 'warning', issue.message, {
            recommendation: issue.recommendation
          });
        });
      } else {
        this.addResult('设备兼容性', 'success', '未发现已知兼容性问题');
      }
      
    } catch (error) {
      this.addResult('设备兼容性', 'error', `检查失败: ${error.message}`);
    }
  }

  /**
   * 获取已知的兼容性问题
   */
  getKnownCompatibilityIssues(systemInfo) {
    const issues = [];
    
    // 检查iOS版本
    if (systemInfo.platform === 'ios') {
      const version = parseFloat(systemInfo.system.replace('iOS ', ''));
      if (version < 10.0) {
        issues.push({
          message: 'iOS版本过低，可能存在蓝牙兼容性问题',
          recommendation: '建议升级到iOS 10.0以上版本'
        });
      }
    }
    
    // 检查Android版本
    if (systemInfo.platform === 'android') {
      const version = parseFloat(systemInfo.system.replace('Android ', ''));
      if (version < 6.0) {
        issues.push({
          message: 'Android版本过低，可能存在蓝牙权限问题',
          recommendation: '建议升级到Android 6.0以上版本'
        });
      }
    }
    
    // 检查特定机型问题
    if (systemInfo.brand && systemInfo.model) {
      const brand = systemInfo.brand.toLowerCase();
      const model = systemInfo.model.toLowerCase();
      
      if (brand.includes('xiaomi') && model.includes('redmi')) {
        issues.push({
          message: '小米/红米设备可能需要特殊处理',
          recommendation: '如遇连接问题，请尝试重启蓝牙或重启设备'
        });
      }
    }
    
    return issues;
  }

  /**
   * 添加诊断结果
   */
  addResult(category, type, message, details = {}) {
    this.diagnosticResults.push({
      category,
      type, // success, warning, error, info
      message,
      details,
      timestamp: new Date().toISOString()
    });
    
    console.log(`[${category}] ${type.toUpperCase()}: ${message}`, details);
  }

  /**
   * 获取诊断报告
   */
  getReport() {
    const summary = {
      total: this.diagnosticResults.length,
      success: this.diagnosticResults.filter(r => r.type === 'success').length,
      warning: this.diagnosticResults.filter(r => r.type === 'warning').length,
      error: this.diagnosticResults.filter(r => r.type === 'error').length,
      info: this.diagnosticResults.filter(r => r.type === 'info').length
    };
    
    return {
      summary,
      results: this.diagnosticResults,
      recommendations: this.getRecommendations()
    };
  }

  /**
   * 获取建议
   */
  getRecommendations() {
    const recommendations = [];
    
    this.diagnosticResults.forEach(result => {
      if (result.details && result.details.recommendation) {
        recommendations.push({
          category: result.category,
          recommendation: result.details.recommendation
        });
      }
    });
    
    return recommendations;
  }
}

module.exports = BluetoothDiagnostic;
