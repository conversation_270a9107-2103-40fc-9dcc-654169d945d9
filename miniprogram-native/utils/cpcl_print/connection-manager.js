/**
 * 蓝牙连接管理器
 * 专门处理蓝牙连接问题，包括10002错误
 */

const BluetoothDiagnostic = require('./bluetooth-diagnostic.js');

class ConnectionManager {
  constructor() {
    this.diagnostic = new BluetoothDiagnostic();
    this.connectionAttempts = new Map(); // 记录连接尝试
    this.deviceStates = new Map(); // 记录设备状态
  }

  /**
   * 智能连接设备（处理10002错误）
   */
  async smartConnect(deviceId, options = {}) {
    const {
      maxRetries = 3,
      retryDelay = 2000,
      preConnectDelay = 1000,
      enableDiagnostic = true
    } = options;

    console.log(`开始智能连接设备: ${deviceId}`);

    // 记录连接尝试
    const attemptKey = `${deviceId}_${Date.now()}`;
    this.connectionAttempts.set(attemptKey, {
      deviceId,
      startTime: Date.now(),
      attempts: 0,
      errors: []
    });

    try {
      // 1. 运行诊断（如果启用）
      if (enableDiagnostic) {
        console.log('运行蓝牙诊断...');
        const diagnosticResults = await this.diagnostic.runFullDiagnostic();
        const hasErrors = diagnosticResults.some(r => r.type === 'error');
        
        if (hasErrors) {
          const errorMessages = diagnosticResults
            .filter(r => r.type === 'error')
            .map(r => r.message)
            .join('; ');
          throw new Error(`诊断发现问题: ${errorMessages}`);
        }
      }

      // 2. 预连接处理
      await this.preConnectProcess(deviceId);

      // 3. 尝试连接（带重试）
      const result = await this.connectWithRetry(deviceId, maxRetries, retryDelay);

      // 4. 连接成功后的处理
      await this.postConnectProcess(deviceId, result);

      console.log(`设备连接成功: ${deviceId}`);
      return result;

    } catch (error) {
      console.error(`设备连接失败: ${deviceId}`, error);
      
      // 记录错误
      const attempt = this.connectionAttempts.get(attemptKey);
      if (attempt) {
        attempt.errors.push({
          message: error.message,
          timestamp: Date.now()
        });
      }

      // 根据错误类型提供具体建议
      const enhancedError = this.enhanceError(error, deviceId);
      throw enhancedError;
    }
  }

  /**
   * 预连接处理
   */
  async preConnectProcess(deviceId) {
    console.log(`预连接处理: ${deviceId}`);

    // 1. 检查设备是否已连接
    try {
      const connectedDevices = await this.getConnectedDevices();
      const alreadyConnected = connectedDevices.find(device => device.deviceId === deviceId);
      
      if (alreadyConnected) {
        console.log('设备已连接，先断开');
        await this.forceDisconnect(deviceId);
        await this.delay(1000);
      }
    } catch (error) {
      console.log('检查已连接设备失败:', error);
    }

    // 2. 清理可能的残留连接
    await this.cleanupConnections(deviceId);

    // 3. 等待设备稳定
    await this.delay(500);
  }

  /**
   * 带重试的连接
   */
  async connectWithRetry(deviceId, maxRetries, retryDelay) {
    let lastError = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      console.log(`连接尝试 ${attempt}/${maxRetries}: ${deviceId}`);

      try {
        // 每次重试前的特殊处理
        if (attempt > 1) {
          await this.retryPreProcess(deviceId, attempt, lastError);
        }

        const result = await this.attemptConnection(deviceId);
        console.log(`连接成功 (尝试 ${attempt}/${maxRetries})`);
        return result;

      } catch (error) {
        lastError = error;
        console.log(`连接失败 (尝试 ${attempt}/${maxRetries}):`, error.message);

        // 分析错误并决定是否继续重试
        const shouldRetry = this.shouldRetryConnection(error, attempt, maxRetries);
        
        if (!shouldRetry) {
          console.log('错误不适合重试，停止尝试');
          break;
        }

        if (attempt < maxRetries) {
          console.log(`${retryDelay}ms后重试...`);
          await this.delay(retryDelay);
        }
      }
    }

    throw lastError || new Error('连接失败');
  }

  /**
   * 重试前的预处理
   */
  async retryPreProcess(deviceId, attemptNumber, lastError) {
    console.log(`重试预处理 (尝试 ${attemptNumber}):`, lastError?.message);

    // 根据错误类型进行不同的处理
    if (lastError?.message?.includes('10002')) {
      // 10002错误的特殊处理
      console.log('处理10002错误...');
      
      // 1. 重置蓝牙适配器
      await this.resetBluetoothAdapter();
      await this.delay(2000);
      
      // 2. 重新初始化
      await this.reinitializeBluetooth();
      await this.delay(1000);
      
    } else if (lastError?.message?.includes('10003')) {
      // 设备未找到，等待更长时间
      console.log('设备未找到，延长等待时间...');
      await this.delay(3000);
      
    } else if (lastError?.message?.includes('10012')) {
      // 连接超时，清理连接
      console.log('连接超时，清理连接...');
      await this.cleanupConnections(deviceId);
      await this.delay(1500);
    }
  }

  /**
   * 尝试连接
   */
  async attemptConnection(deviceId) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('连接超时'));
      }, 15000);

      wx.createBLEConnection({
        deviceId,
        success: (res) => {
          clearTimeout(timeout);
          console.log('BLE连接建立成功:', res);
          resolve({ deviceId, ...res });
        },
        fail: (res) => {
          clearTimeout(timeout);
          console.error('BLE连接失败:', res);
          
          // 创建详细的错误信息
          const error = new Error(this.getErrorMessage(res));
          error.errCode = res.errCode;
          error.originalError = res;
          reject(error);
        }
      });
    });
  }

  /**
   * 获取错误信息
   */
  getErrorMessage(res) {
    const errorMessages = {
      10002: '设备连接失败：设备不可连接或已被其他应用占用',
      10003: '设备连接失败：设备未找到或已关闭',
      10012: '设备连接失败：连接超时',
      10009: '设备连接失败：系统内部错误',
      '-1': '设备连接失败：系统错误'
    };

    return errorMessages[res.errCode] || `连接失败：${res.errMsg || '未知错误'}`;
  }

  /**
   * 判断是否应该重试
   */
  shouldRetryConnection(error, attempt, maxRetries) {
    if (attempt >= maxRetries) {
      return false;
    }

    // 根据错误码决定是否重试
    const retryableErrors = [10002, 10003, 10012, 10009];
    const nonRetryableErrors = [-1, 10000, 10001];

    if (error.errCode && nonRetryableErrors.includes(error.errCode)) {
      return false;
    }

    if (error.errCode && retryableErrors.includes(error.errCode)) {
      return true;
    }

    // 默认重试
    return true;
  }

  /**
   * 连接后处理
   */
  async postConnectProcess(deviceId, connectionResult) {
    console.log(`连接后处理: ${deviceId}`);

    // 更新设备状态
    this.deviceStates.set(deviceId, {
      connected: true,
      connectedAt: Date.now(),
      connectionResult
    });

    // 等待连接稳定
    await this.delay(1000);
  }

  /**
   * 获取已连接设备
   */
  async getConnectedDevices() {
    return new Promise((resolve, reject) => {
      wx.getConnectedBluetoothDevices({
        services: [],
        success: (res) => resolve(res.devices || []),
        fail: reject
      });
    });
  }

  /**
   * 强制断开连接
   */
  async forceDisconnect(deviceId) {
    return new Promise((resolve) => {
      wx.closeBLEConnection({
        deviceId,
        success: () => {
          console.log(`强制断开连接成功: ${deviceId}`);
          resolve();
        },
        fail: (err) => {
          console.log(`强制断开连接失败: ${deviceId}`, err);
          resolve(); // 即使失败也继续
        }
      });
    });
  }

  /**
   * 清理连接
   */
  async cleanupConnections(deviceId) {
    console.log(`清理连接: ${deviceId}`);
    
    try {
      await this.forceDisconnect(deviceId);
      await this.delay(500);
    } catch (error) {
      console.log('清理连接失败:', error);
    }
  }

  /**
   * 重置蓝牙适配器
   */
  async resetBluetoothAdapter() {
    console.log('重置蓝牙适配器...');
    
    return new Promise((resolve) => {
      wx.closeBluetoothAdapter({
        complete: () => {
          console.log('蓝牙适配器已关闭');
          resolve();
        }
      });
    });
  }

  /**
   * 重新初始化蓝牙
   */
  async reinitializeBluetooth() {
    console.log('重新初始化蓝牙...');
    
    return new Promise((resolve, reject) => {
      wx.openBluetoothAdapter({
        success: (res) => {
          console.log('蓝牙重新初始化成功');
          resolve(res);
        },
        fail: (err) => {
          console.error('蓝牙重新初始化失败:', err);
          reject(err);
        }
      });
    });
  }

  /**
   * 增强错误信息
   */
  enhanceError(error, deviceId) {
    const enhancedError = new Error(error.message);
    enhancedError.originalError = error;
    enhancedError.deviceId = deviceId;
    
    // 添加解决建议
    const suggestions = this.getErrorSuggestions(error);
    enhancedError.suggestions = suggestions;
    
    return enhancedError;
  }

  /**
   * 获取错误建议
   */
  getErrorSuggestions(error) {
    const suggestions = [];
    
    if (error.message?.includes('10002')) {
      suggestions.push('确保打印机处于配对模式');
      suggestions.push('检查打印机是否被其他设备连接');
      suggestions.push('尝试重启打印机');
      suggestions.push('靠近打印机再次尝试');
    }
    
    if (error.message?.includes('10003')) {
      suggestions.push('确保打印机已开启');
      suggestions.push('检查打印机蓝牙是否可见');
      suggestions.push('重新搜索设备');
    }
    
    if (error.message?.includes('10012')) {
      suggestions.push('检查蓝牙信号强度');
      suggestions.push('靠近设备重试');
      suggestions.push('检查设备电量');
    }
    
    return suggestions;
  }

  /**
   * 延时函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取连接统计
   */
  getConnectionStats() {
    const stats = {
      totalAttempts: this.connectionAttempts.size,
      deviceStates: Object.fromEntries(this.deviceStates),
      recentAttempts: Array.from(this.connectionAttempts.values())
        .filter(attempt => Date.now() - attempt.startTime < 300000) // 最近5分钟
    };
    
    return stats;
  }
}

module.exports = ConnectionManager;
