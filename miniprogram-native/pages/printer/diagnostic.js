// pages/printer/diagnostic.js
const BluetoothDiagnostic = require('../../utils/cpcl_print/bluetooth-diagnostic.js');
const ConnectionManager = require('../../utils/cpcl_print/connection-manager.js');

Page({
  data: {
    diagnosticResults: [],
    isRunning: false,
    connectionStats: null,
    testDeviceId: '',
    showAdvanced: false
  },

  onLoad(options) {
    this.diagnostic = new BluetoothDiagnostic();
    this.connectionManager = new ConnectionManager();
    
    // 如果传入了设备ID，设置为测试设备
    if (options.deviceId) {
      this.setData({
        testDeviceId: options.deviceId
      });
    }
  },

  // 运行完整诊断
  async runDiagnostic() {
    if (this.data.isRunning) return;

    this.setData({
      isRunning: true,
      diagnosticResults: []
    });

    wx.showLoading({
      title: '诊断中...',
      mask: true
    });

    try {
      const results = await this.diagnostic.runFullDiagnostic();
      
      this.setData({
        diagnosticResults: results,
        isRunning: false
      });

      wx.hideLoading();

      // 显示诊断摘要
      this.showDiagnosticSummary(results);

    } catch (error) {
      wx.hideLoading();
      this.setData({ isRunning: false });
      
      wx.showModal({
        title: '诊断失败',
        content: error.message || '诊断过程中出现错误',
        showCancel: false
      });
    }
  },

  // 显示诊断摘要
  showDiagnosticSummary(results) {
    const report = this.diagnostic.getReport();
    const { summary, recommendations } = report;

    let content = `检查项目: ${summary.total}\n`;
    content += `✓ 正常: ${summary.success}\n`;
    content += `⚠ 警告: ${summary.warning}\n`;
    content += `✗ 错误: ${summary.error}\n`;

    if (recommendations.length > 0) {
      content += `\n建议:\n`;
      recommendations.slice(0, 3).forEach((rec, index) => {
        content += `${index + 1}. ${rec.recommendation}\n`;
      });
    }

    wx.showModal({
      title: '诊断完成',
      content: content,
      showCancel: false,
      confirmText: '查看详情'
    });
  },

  // 测试连接指定设备
  async testConnection() {
    if (!this.data.testDeviceId) {
      wx.showModal({
        title: '提示',
        content: '请先输入要测试的设备ID',
        showCancel: false
      });
      return;
    }

    wx.showLoading({
      title: '测试连接...',
      mask: true
    });

    try {
      const result = await this.connectionManager.smartConnect(this.data.testDeviceId, {
        maxRetries: 2,
        enableDiagnostic: false
      });

      wx.hideLoading();

      wx.showModal({
        title: '连接测试成功',
        content: `设备 ${this.data.testDeviceId} 连接成功！`,
        showCancel: false
      });

      // 更新连接统计
      this.updateConnectionStats();

    } catch (error) {
      wx.hideLoading();

      let content = `连接失败: ${error.message}`;
      if (error.suggestions && error.suggestions.length > 0) {
        content += `\n\n建议:\n• ${error.suggestions.join('\n• ')}`;
      }

      wx.showModal({
        title: '连接测试失败',
        content: content,
        showCancel: false
      });
    }
  },

  // 更新连接统计
  updateConnectionStats() {
    const stats = this.connectionManager.getConnectionStats();
    this.setData({
      connectionStats: stats
    });
  },

  // 输入设备ID
  onDeviceIdInput(e) {
    this.setData({
      testDeviceId: e.detail.value
    });
  },

  // 切换高级选项显示
  toggleAdvanced() {
    this.setData({
      showAdvanced: !this.data.showAdvanced
    });
  },

  // 清除缓存
  async clearCache() {
    try {
      wx.removeStorageSync('bluetoothPrintList');
      wx.removeStorageSync('connectedDevice');
      
      wx.showToast({
        title: '缓存已清除',
        icon: 'success'
      });
    } catch (error) {
      wx.showModal({
        title: '清除失败',
        content: error.message || '清除缓存失败',
        showCancel: false
      });
    }
  },

  // 重置蓝牙
  async resetBluetooth() {
    wx.showLoading({
      title: '重置中...',
      mask: true
    });

    try {
      // 关闭蓝牙适配器
      await new Promise((resolve) => {
        wx.closeBluetoothAdapter({
          complete: resolve
        });
      });

      // 等待一段时间
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 重新打开蓝牙适配器
      await new Promise((resolve, reject) => {
        wx.openBluetoothAdapter({
          success: resolve,
          fail: reject
        });
      });

      wx.hideLoading();

      wx.showToast({
        title: '重置成功',
        icon: 'success'
      });

    } catch (error) {
      wx.hideLoading();
      
      wx.showModal({
        title: '重置失败',
        content: error.message || '重置蓝牙失败',
        showCancel: false
      });
    }
  },

  // 导出诊断报告
  exportReport() {
    if (this.data.diagnosticResults.length === 0) {
      wx.showModal({
        title: '提示',
        content: '请先运行诊断',
        showCancel: false
      });
      return;
    }

    const report = this.diagnostic.getReport();
    const reportText = this.formatReportText(report);

    // 复制到剪贴板
    wx.setClipboardData({
      data: reportText,
      success: () => {
        wx.showToast({
          title: '报告已复制',
          icon: 'success'
        });
      }
    });
  },

  // 格式化报告文本
  formatReportText(report) {
    let text = '=== 蓝牙诊断报告 ===\n';
    text += `生成时间: ${new Date().toLocaleString()}\n\n`;
    
    text += '=== 摘要 ===\n';
    text += `总检查项: ${report.summary.total}\n`;
    text += `正常: ${report.summary.success}\n`;
    text += `警告: ${report.summary.warning}\n`;
    text += `错误: ${report.summary.error}\n\n`;

    text += '=== 详细结果 ===\n';
    report.results.forEach((result, index) => {
      text += `${index + 1}. [${result.category}] ${result.type.toUpperCase()}: ${result.message}\n`;
      if (result.details && result.details.recommendation) {
        text += `   建议: ${result.details.recommendation}\n`;
      }
      text += '\n';
    });

    if (report.recommendations.length > 0) {
      text += '=== 建议 ===\n';
      report.recommendations.forEach((rec, index) => {
        text += `${index + 1}. [${rec.category}] ${rec.recommendation}\n`;
      });
    }

    return text;
  },

  // 获取结果类型的图标
  getResultIcon(type) {
    const icons = {
      success: '✓',
      warning: '⚠',
      error: '✗',
      info: 'ℹ'
    };
    return icons[type] || '•';
  },

  // 获取结果类型的颜色类
  getResultClass(type) {
    const classes = {
      success: 'result-success',
      warning: 'result-warning', 
      error: 'result-error',
      info: 'result-info'
    };
    return classes[type] || '';
  },

  // 查看结果详情
  viewResultDetail(e) {
    const index = e.currentTarget.dataset.index;
    const result = this.data.diagnosticResults[index];
    
    if (!result) return;

    let content = result.message;
    if (result.details) {
      if (result.details.recommendation) {
        content += `\n\n建议: ${result.details.recommendation}`;
      }
      if (result.details.errCode) {
        content += `\n错误码: ${result.details.errCode}`;
      }
    }

    wx.showModal({
      title: result.category,
      content: content,
      showCancel: false
    });
  }
});
