<!--pages/printer/diagnostic.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="header">
    <text class="title">蓝牙连接诊断</text>
    <text class="subtitle">解决蓝牙打印机连接问题</text>
  </view>

  <!-- 快速诊断 -->
  <view class="section">
    <view class="section-title">
      <text>快速诊断</text>
    </view>
    <view class="section-content">
      <button 
        class="btn-primary" 
        bindtap="runDiagnostic" 
        disabled="{{isRunning}}"
      >
        {{isRunning ? '诊断中...' : '开始诊断'}}
      </button>
      <text class="help-text">检查蓝牙状态、权限和设备兼容性</text>
    </view>
  </view>

  <!-- 连接测试 -->
  <view class="section">
    <view class="section-title">
      <text>连接测试</text>
    </view>
    <view class="section-content">
      <view class="input-group">
        <text class="input-label">设备ID:</text>
        <input 
          class="input-field" 
          placeholder="输入要测试的设备ID" 
          value="{{testDeviceId}}"
          bindinput="onDeviceIdInput"
        />
      </view>
      <button 
        class="btn-secondary" 
        bindtap="testConnection"
        disabled="{{!testDeviceId}}"
      >
        测试连接
      </button>
    </view>
  </view>

  <!-- 诊断结果 -->
  <view class="section" wx:if="{{diagnosticResults.length > 0}}">
    <view class="section-title">
      <text>诊断结果</text>
      <text class="result-count">({{diagnosticResults.length}}项)</text>
    </view>
    <view class="section-content">
      <view class="results-list">
        <view 
          class="result-item {{getResultClass(item.type)}}" 
          wx:for="{{diagnosticResults}}" 
          wx:key="index"
          bindtap="viewResultDetail"
          data-index="{{index}}"
        >
          <view class="result-header">
            <text class="result-icon">{{getResultIcon(item.type)}}</text>
            <text class="result-category">{{item.category}}</text>
          </view>
          <text class="result-message">{{item.message}}</text>
          <text class="result-time">{{item.timestamp}}</text>
        </view>
      </view>
      
      <view class="result-actions">
        <button class="btn-outline" bindtap="exportReport">
          导出报告
        </button>
      </view>
    </view>
  </view>

  <!-- 高级选项 -->
  <view class="section">
    <view class="section-title" bindtap="toggleAdvanced">
      <text>高级选项</text>
      <text class="toggle-icon">{{showAdvanced ? '▼' : '▶'}}</text>
    </view>
    <view class="section-content" wx:if="{{showAdvanced}}">
      <view class="advanced-options">
        <button class="btn-outline" bindtap="clearCache">
          清除缓存
        </button>
        <button class="btn-outline" bindtap="resetBluetooth">
          重置蓝牙
        </button>
      </view>
      
      <!-- 连接统计 -->
      <view class="stats" wx:if="{{connectionStats}}">
        <text class="stats-title">连接统计</text>
        <view class="stats-item">
          <text>总尝试次数: {{connectionStats.totalAttempts}}</text>
        </view>
        <view class="stats-item">
          <text>最近尝试: {{connectionStats.recentAttempts.length}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 常见问题 -->
  <view class="section">
    <view class="section-title">
      <text>常见问题解决方案</text>
    </view>
    <view class="section-content">
      <view class="faq-list">
        <view class="faq-item">
          <text class="faq-question">错误码10002：连接失败</text>
          <text class="faq-answer">
            • 确保打印机处于配对模式
            • 检查打印机是否被其他设备连接
            • 尝试重启打印机和手机蓝牙
            • 靠近打印机再次尝试
          </text>
        </view>
        
        <view class="faq-item">
          <text class="faq-question">错误码10003：设备未找到</text>
          <text class="faq-answer">
            • 确保打印机已开启
            • 检查打印机蓝牙是否可见
            • 重新搜索设备
            • 检查设备距离是否过远
          </text>
        </view>
        
        <view class="faq-item">
          <text class="faq-question">错误码10012：连接超时</text>
          <text class="faq-answer">
            • 检查蓝牙信号强度
            • 靠近设备重试
            • 检查设备电量
            • 重启蓝牙适配器
          </text>
        </view>
        
        <view class="faq-item">
          <text class="faq-question">搜索不到设备</text>
          <text class="faq-answer">
            • 确保已开启位置权限（Android）
            • 检查蓝牙权限设置
            • 重启小程序
            • 清除蓝牙缓存
          </text>
        </view>
      </view>
    </view>
  </view>

  <!-- 帮助信息 -->
  <view class="help-section">
    <text class="help-title">使用说明</text>
    <text class="help-content">
      1. 首先运行"快速诊断"检查基本环境
      2. 如果有具体设备连接问题，使用"连接测试"
      3. 查看诊断结果中的具体建议
      4. 尝试"高级选项"中的修复方法
      5. 参考"常见问题"中的解决方案
    </text>
  </view>
</view>
