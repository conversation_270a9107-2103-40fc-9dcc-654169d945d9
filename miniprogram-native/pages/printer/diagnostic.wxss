/* pages/printer/diagnostic.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  color: white;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  opacity: 0.9;
}

/* 区块样式 */
.section {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  padding: 30rpx;
  background-color: #f8f9fa;
  border-bottom: 2rpx solid #e9ecef;
  font-size: 32rpx;
  font-weight: bold;
  color: #495057;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-content {
  padding: 30rpx;
}

.toggle-icon {
  font-size: 24rpx;
  color: #6c757d;
}

/* 按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 30rpx;
  margin-bottom: 20rpx;
  width: 100%;
}

.btn-primary[disabled] {
  background: #6c757d;
  opacity: 0.6;
}

.btn-secondary {
  background: #28a745;
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 30rpx;
  width: 100%;
}

.btn-secondary[disabled] {
  background: #6c757d;
  opacity: 0.6;
}

.btn-outline {
  background: transparent;
  color: #667eea;
  border: 2rpx solid #667eea;
  border-radius: 50rpx;
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  margin: 10rpx;
}

.help-text {
  display: block;
  font-size: 24rpx;
  color: #6c757d;
  text-align: center;
  margin-top: 10rpx;
}

/* 输入组 */
.input-group {
  margin-bottom: 30rpx;
}

.input-label {
  display: block;
  font-size: 28rpx;
  color: #495057;
  margin-bottom: 10rpx;
}

.input-field {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 10rpx;
  font-size: 28rpx;
  background: #f8f9fa;
}

/* 诊断结果 */
.result-count {
  font-size: 24rpx;
  color: #6c757d;
  font-weight: normal;
}

.results-list {
  margin-bottom: 30rpx;
}

.result-item {
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
  border-left: 8rpx solid #e9ecef;
}

.result-success {
  background-color: #d4edda;
  border-left-color: #28a745;
}

.result-warning {
  background-color: #fff3cd;
  border-left-color: #ffc107;
}

.result-error {
  background-color: #f8d7da;
  border-left-color: #dc3545;
}

.result-info {
  background-color: #d1ecf1;
  border-left-color: #17a2b8;
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.result-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
  font-weight: bold;
}

.result-category {
  font-size: 28rpx;
  font-weight: bold;
  color: #495057;
}

.result-message {
  display: block;
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.5;
  margin-bottom: 10rpx;
}

.result-time {
  display: block;
  font-size: 22rpx;
  color: #adb5bd;
}

.result-actions {
  text-align: center;
  padding-top: 20rpx;
  border-top: 2rpx solid #e9ecef;
}

/* 高级选项 */
.advanced-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  margin-bottom: 30rpx;
}

.stats {
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-top: 20rpx;
}

.stats-title {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #495057;
  margin-bottom: 15rpx;
}

.stats-item {
  font-size: 26rpx;
  color: #6c757d;
  margin-bottom: 10rpx;
}

/* 常见问题 */
.faq-list {
  margin-top: 20rpx;
}

.faq-item {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 10rpx;
}

.faq-question {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #495057;
  margin-bottom: 15rpx;
}

.faq-answer {
  display: block;
  font-size: 26rpx;
  color: #6c757d;
  line-height: 1.6;
  white-space: pre-line;
}

/* 帮助信息 */
.help-section {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  padding: 30rpx;
  border-radius: 20rpx;
  color: white;
  margin-top: 40rpx;
}

.help-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.help-content {
  display: block;
  font-size: 26rpx;
  line-height: 1.6;
  opacity: 0.9;
}
